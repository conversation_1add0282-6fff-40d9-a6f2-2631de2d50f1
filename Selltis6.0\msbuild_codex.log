Build started 9/19/2025 3:36:57 PM.
Overriding target "GetFrameworkPaths" in project "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" with target "GetFrameworkPaths" from project "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.NETFramework.targets".
Overriding target "SatelliteDllsProjectOutputGroup" in project "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" with target "SatelliteDllsProjectOutputGroup" from project "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.WinFX.targets".
Project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" on node 1 (Build target(s)).
Building with tools version "Current".
Project file contains ToolsVersion="Current". This toolset may be unknown or missing, in which case you may be able to resolve this by installing the appropriate version of MSBuild, or the build may have been forced to a particular ToolsVersion for policy reasons. Treating the project as if it had ToolsVersion="4.0". For more information, please see http://go.microsoft.com/fwlink/?LinkId=291333.
Target "_CheckForInvalidConfigurationAndPlatform" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (entry point):
Task "Error" skipped, due to false condition; ( '$(_InvalidConfigurationError)' == 'true' ) was evaluated as ( '' == 'true' ).
Task "Warning" skipped, due to false condition; ( '$(_InvalidConfigurationWarning)' == 'true' ) was evaluated as ( '' == 'true' ).
Using "Message" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "Message"
  Configuration=Debug
Done executing task "Message".
Task "Message"
  Platform=AnyCPU
Done executing task "Message".
Task "Error" skipped, due to false condition; ('$(OutDir)' != '' and !HasTrailingSlash('$(OutDir)')) was evaluated as ('bin\' != '' and !HasTrailingSlash('bin\')).
Task "Error" skipped, due to false condition; ('$(BaseIntermediateOutputPath)' != '' and !HasTrailingSlash('$(BaseIntermediateOutputPath)')) was evaluated as ('obj\' != '' and !HasTrailingSlash('obj\')).
Task "Error" skipped, due to false condition; ('$(IntermediateOutputPath)' != '' and !HasTrailingSlash('$(IntermediateOutputPath)')) was evaluated as ('obj\Debug\' != '' and !HasTrailingSlash('obj\Debug\')).
Done building target "_CheckForInvalidConfigurationAndPlatform" in project "APICodex.csproj".
Target "EntityDeploy" skipped, due to false condition; ('@(EntityDeploy)' != '') was evaluated as ('' != '').
Target "BeforeBuild" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (target "Build" depends on it):
Done building target "BeforeBuild" in project "APICodex.csproj".
Target "BuildOnlySettings" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (target "CoreBuild" depends on it):
Done building target "BuildOnlySettings" in project "APICodex.csproj".
Target "GetFrameworkPaths" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.NETFramework.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (target "PrepareForBuild" depends on it):
Done building target "GetFrameworkPaths" in project "APICodex.csproj".
Target "GetWinFXPath" skipped, due to false condition; (('@(Page)' != '' or '@(ApplicationDefinition)' != '' or '@(Resource)' != '') and ('$(GetWinFXNativePath)' != '' or '$(GetWinFXWoWPath)' != '' )) was evaluated as (('' != '' or '' != '' or '' != '') and ('' != '' or '' != '' )).
Target "GetReferenceAssemblyPaths" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (target "PrepareForBuild" depends on it):
Using "GetReferenceAssemblyPaths" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "GetReferenceAssemblyPaths"
Done executing task "GetReferenceAssemblyPaths".
Done building target "GetReferenceAssemblyPaths" in project "APICodex.csproj".
Target "PrepareForBuild" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (target "CoreBuild" depends on it):
Using "FindAppConfigFile" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "FindAppConfigFile"
Done executing task "FindAppConfigFile".
Using "MakeDir" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "MakeDir"
Done executing task "MakeDir".
Done building target "PrepareForBuild" in project "APICodex.csproj".
Target "PreBuildEvent" skipped, due to false condition; ('$(PreBuildEvent)'!='') was evaluated as (''!='').
Target "BeforeResolveReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (target "ResolveReferences" depends on it):
Done building target "BeforeResolveReferences" in project "APICodex.csproj".
Target "AssignProjectConfiguration" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (target "ResolveReferences" depends on it):
Using "AssignProjectConfiguration" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "AssignProjectConfiguration"
  Project reference "..\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" has not been resolved.
  Project reference "..\Selltis6.0\Selltis.Core\Selltis.Core.csproj" has not been resolved.
  Project reference "..\Selltis6.0\Selltis6.0\Selltis.MVC.csproj" has not been resolved.
Done executing task "AssignProjectConfiguration".
Done building target "AssignProjectConfiguration" in project "APICodex.csproj".
Target "AssignProjectConfiguration" skipped. Previously built successfully.
Target "_SplitProjectReferencesByFileExistence" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (target "ResolveProjectReferences" depends on it):
Task "ResolveNonMSBuildProjectOutput" skipped, due to false condition; ('$(BuildingInsideVisualStudio)'=='true' and '@(ProjectReferenceWithConfiguration)'!='') was evaluated as (''=='true' and '..\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj;..\Selltis6.0\Selltis.Core\Selltis.Core.csproj;..\Selltis6.0\Selltis6.0\Selltis.MVC.csproj'!='').
Done building target "_SplitProjectReferencesByFileExistence" in project "APICodex.csproj".
Target "ResolveProjectReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (target "ResolveReferences" depends on it):
Task "MSBuild" skipped, due to false condition; ('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and ('$(BuildingInsideVisualStudio)' == 'true' or '$(BuildProjectReferences)' != 'true') and '$(VisualStudioVersion)' != '10.0' and '@(_MSBuildProjectReferenceExistent)' != '') was evaluated as ('true' == 'true' and '..\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj;..\Selltis6.0\Selltis.Core\Selltis.Core.csproj;..\Selltis6.0\Selltis6.0\Selltis.MVC.csproj' != '' and ('' == 'true' or 'true' != 'true') and '11.0' != '10.0' and '..\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj;..\Selltis6.0\Selltis.Core\Selltis.Core.csproj;..\Selltis6.0\Selltis6.0\Selltis.MVC.csproj' != '').
Task "MSBuild" skipped, due to false condition; ('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and ('$(BuildingInsideVisualStudio)' == 'true' or '$(BuildProjectReferences)' != 'true') and '$(VisualStudioVersion)' == '10.0' and '@(_MSBuildProjectReferenceExistent)' != '') was evaluated as ('true' == 'true' and '..\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj;..\Selltis6.0\Selltis.Core\Selltis.Core.csproj;..\Selltis6.0\Selltis6.0\Selltis.MVC.csproj' != '' and ('' == 'true' or 'true' != 'true') and '11.0' == '10.0' and '..\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj;..\Selltis6.0\Selltis.Core\Selltis.Core.csproj;..\Selltis6.0\Selltis6.0\Selltis.MVC.csproj' != '').
Using "MSBuild" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "MSBuild"
Overriding target "GetFrameworkPaths" in project "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" with target "GetFrameworkPaths" from project "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.NETFramework.targets".
Overriding target "SatelliteDllsProjectOutputGroup" in project "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" with target "SatelliteDllsProjectOutputGroup" from project "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.WinFX.targets".
Project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (1) is building "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (2) on node 1 (default targets).
Building with tools version "12.0".
Project file contains ToolsVersion="12.0". This toolset may be unknown or missing, in which case you may be able to resolve this by installing the appropriate version of MSBuild, or the build may have been forced to a particular ToolsVersion for policy reasons. Treating the project as if it had ToolsVersion="4.0". For more information, please see http://go.microsoft.com/fwlink/?LinkId=291333.
Target "_CheckForInvalidConfigurationAndPlatform" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (entry point):
Task "Error" skipped, due to false condition; ( '$(_InvalidConfigurationError)' == 'true' ) was evaluated as ( '' == 'true' ).
Task "Warning" skipped, due to false condition; ( '$(_InvalidConfigurationWarning)' == 'true' ) was evaluated as ( '' == 'true' ).
Task "Message"
  Configuration=Debug
Done executing task "Message".
Task "Message"
  Platform=AnyCPU
Done executing task "Message".
Task "Error" skipped, due to false condition; ('$(OutDir)' != '' and !HasTrailingSlash('$(OutDir)')) was evaluated as ('bin\Debug\' != '' and !HasTrailingSlash('bin\Debug\')).
Task "Error" skipped, due to false condition; ('$(BaseIntermediateOutputPath)' != '' and !HasTrailingSlash('$(BaseIntermediateOutputPath)')) was evaluated as ('obj\' != '' and !HasTrailingSlash('obj\')).
Task "Error" skipped, due to false condition; ('$(IntermediateOutputPath)' != '' and !HasTrailingSlash('$(IntermediateOutputPath)')) was evaluated as ('obj\Debug\' != '' and !HasTrailingSlash('obj\Debug\')).
Done building target "_CheckForInvalidConfigurationAndPlatform" in project "Selltis.Data.csproj".
Target "EntityDeploy" skipped, due to false condition; ('@(EntityDeploy)' != '') was evaluated as ('' != '').
Target "BeforeBuild" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "Build" depends on it):
Done building target "BeforeBuild" in project "Selltis.Data.csproj".
Target "BuildOnlySettings" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "CoreBuild" depends on it):
Done building target "BuildOnlySettings" in project "Selltis.Data.csproj".
Target "GetFrameworkPaths" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.NETFramework.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PrepareForBuild" depends on it):
Done building target "GetFrameworkPaths" in project "Selltis.Data.csproj".
Target "GetWinFXPath" skipped, due to false condition; (('@(Page)' != '' or '@(ApplicationDefinition)' != '' or '@(Resource)' != '') and ('$(GetWinFXNativePath)' != '' or '$(GetWinFXWoWPath)' != '' )) was evaluated as (('' != '' or '' != '' or '' != '') and ('' != '' or '' != '' )).
Target "GetReferenceAssemblyPaths" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PrepareForBuild" depends on it):
Task "GetReferenceAssemblyPaths"
Done executing task "GetReferenceAssemblyPaths".
Done building target "GetReferenceAssemblyPaths" in project "Selltis.Data.csproj".
Target "PrepareForBuild" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "CoreBuild" depends on it):
Task "FindAppConfigFile"
  Found "app.config".
Done executing task "FindAppConfigFile".
Task "MakeDir"
Done executing task "MakeDir".
Done building target "PrepareForBuild" in project "Selltis.Data.csproj".
Target "PreBuildEvent" skipped, due to false condition; ('$(PreBuildEvent)'!='') was evaluated as (''!='').
Target "BeforeResolveReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "ResolveReferences" depends on it):
Done building target "BeforeResolveReferences" in project "Selltis.Data.csproj".
Target "AssignProjectConfiguration" skipped, due to false condition; ('$(CurrentSolutionConfigurationContents)' != '' or '@(ProjectReference)'!='') was evaluated as ('' != '' or ''!='').
Target "AssignProjectConfiguration" skipped, due to false condition; ('$(CurrentSolutionConfigurationContents)' != '' or '@(ProjectReference)'!='') was evaluated as ('' != '' or ''!='').
Target "_SplitProjectReferencesByFileExistence" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "ResolveProjectReferences" depends on it):
Task "ResolveNonMSBuildProjectOutput" skipped, due to false condition; ('$(BuildingInsideVisualStudio)'=='true' and '@(ProjectReferenceWithConfiguration)'!='') was evaluated as (''=='true' and ''!='').
Done building target "_SplitProjectReferencesByFileExistence" in project "Selltis.Data.csproj".
Target "ResolveProjectReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "ResolveReferences" depends on it):
Task "MSBuild" skipped, due to false condition; ('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and ('$(BuildingInsideVisualStudio)' == 'true' or '$(BuildProjectReferences)' != 'true') and '$(VisualStudioVersion)' != '10.0' and '@(_MSBuildProjectReferenceExistent)' != '') was evaluated as ('' == 'true' and '' != '' and ('' == 'true' or 'true' != 'true') and '11.0' != '10.0' and '' != '').
Task "MSBuild" skipped, due to false condition; ('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and ('$(BuildingInsideVisualStudio)' == 'true' or '$(BuildProjectReferences)' != 'true') and '$(VisualStudioVersion)' == '10.0' and '@(_MSBuildProjectReferenceExistent)' != '') was evaluated as ('' == 'true' and '' != '' and ('' == 'true' or 'true' != 'true') and '11.0' == '10.0' and '' != '').
Task "MSBuild" skipped, due to false condition; ('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and '$(BuildingInsideVisualStudio)' != 'true' and '$(BuildProjectReferences)' == 'true' and '@(_MSBuildProjectReferenceExistent)' != '') was evaluated as ('' == 'true' and '' != '' and '' != 'true' and 'true' == 'true' and '' != '').
Task "MSBuild" skipped, due to false condition; ('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and '$(BuildingProject)' == 'true' and '@(_MSBuildProjectReferenceExistent)' != '') was evaluated as ('' == 'true' and '' != '' and 'true' == 'true' and '' != '').
Task "Warning" skipped, due to false condition; ('@(ProjectReferenceWithConfiguration)' != '' and '@(_MSBuildProjectReferenceNonexistent)' != '') was evaluated as ('' != '' and '' != '').
Done building target "ResolveProjectReferences" in project "Selltis.Data.csproj".
Target "ResolveNativeReferences" skipped, due to false condition; ('@(NativeReference)'!='') was evaluated as (''!='').
Target "GetFrameworkPaths" skipped. Previously built successfully.
Target "GetReferenceAssemblyPaths" skipped. Previously built successfully.
Target "PrepareForBuild" skipped. Previously built successfully.
Target "GetInstalledSDKLocations" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "ResolveSDKReferences" depends on it):
Task "GetInstalledSDKLocations" skipped, due to false condition; ('@(SDKReference)' != '') was evaluated as ('' != '').
Done building target "GetInstalledSDKLocations" in project "Selltis.Data.csproj".
Target "ResolveSDKReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "ResolveAssemblyReferences" depends on it):
Task "ResolveSDKReference" skipped, due to false condition; ('@(SDKReference)'!='') was evaluated as (''!='').
Done building target "ResolveSDKReferences" in project "Selltis.Data.csproj".
Target "ResolveSDKReferences" skipped. Previously built successfully.
Target "ExpandSDKReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "ResolveAssemblyReferences" depends on it):
Task "GetSDKReferenceFiles" skipped, due to false condition; ('@(ResolvedSDKReference)'!='') was evaluated as (''!='').
Done building target "ExpandSDKReferences" in project "Selltis.Data.csproj".
Target "ResolveAssemblyReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "ResolveReferences" depends on it):
Using "ResolveAssemblyReference" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "ResolveAssemblyReference"
  TargetFrameworkMoniker:
      .NETFramework,Version=v4.8
  TargetFrameworkMonikerDisplayName:
      .NET Framework 4.8
  TargetedRuntimeVersion:
      v4.0.30319
  Assemblies:
      Microsoft.CSharp
      Microsoft.VisualBasic
      DocumentFormat.OpenXml
          HintPath = '..\Common\DocumentFormat.OpenXml.dll'
      ephtmltopdf, Version=*******, Culture=neutral, PublicKeyToken=5b5f377bc08a4d32, processorArchitecture=MSIL
          HintPath = '..\packages\CommonDLLs\ephtmltopdf.dll'
          SpecificVersion = 'False'
      Google.GData.Client
          HintPath = '..\packages\CommonDLLs\Google.GData.Client.dll'
      IMAP4.Net
          HintPath = '..\packages\CommonDLLs\IMAP4.Net.dll'
      ImapUtility
          HintPath = '..\packages\CommonDLLs\ImapUtility.dll'
      MailBee.NET
          HintPath = '..\packages\CommonDLLs\MailBee.NET.dll'
      Microsoft.WindowsAzure.Configuration, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL
          HintPath = '..\packages\CommonDLLs\Microsoft.WindowsAzure.Configuration.dll'
          SpecificVersion = 'False'
      Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL
          HintPath = '..\packages\CommonDLLs\Microsoft.WindowsAzure.Storage.dll'
          SpecificVersion = 'False'
      Microsoft.WindowsAzure.StorageClient, Version=1.7.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL
          HintPath = '..\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll'
          SpecificVersion = 'False'
      POP3.Net
          HintPath = '..\packages\CommonDLLs\POP3.Net.dll'
      PublicDomain
          HintPath = '..\packages\CommonDLLs\PublicDomain.dll'
      Quickwebsoft.Web.UI.WebControls.EventCalendar
          HintPath = '..\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll'
      SautinSoft.Document
          HintPath = '..\Common\SautinSoft.Document.dll'
      SMTP.Net
          HintPath = '..\packages\CommonDLLs\SMTP.Net.dll'
      System
      System.configuration
      System.Data
      System.Drawing
      System.Runtime.Serialization
      System.Web
      System.Web.ApplicationServices
      System.Web.Extensions
      System.Web.Mvc
          HintPath = '..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll'
      System.Xml
      System.Xml.Linq
      System.Data.DataSetExtensions
      Telerik.Windows.Documents.Core
          HintPath = '..\Common\telerik DLLs\Telerik.Windows.Documents.Core.dll'
      Telerik.Windows.Documents.Fixed
          HintPath = '..\Common\telerik DLLs\Telerik.Windows.Documents.Fixed.dll'
      Telerik.Windows.Documents.Flow
          HintPath = '..\Common\telerik DLLs\Telerik.Windows.Documents.Flow.dll'
      Telerik.Windows.Documents.Flow.FormatProviders.Pdf
          HintPath = '..\Common\telerik DLLs\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll'
      Telerik.Windows.Zip
          HintPath = '..\Common\telerik DLLs\Telerik.Windows.Zip.dll'
      UltimateAjax
          HintPath = '..\packages\CommonDLLs\UltimateAjax.dll'
      WindowsBase
      System.Core
  AssemblyFiles:
      C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll
  CandidateAssemblyFiles:
  TargetFrameworkDirectories:
      C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\,C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\
  InstalledAssemblyTables:
  IgnoreInstalledAssemblyTable:
      False
  SearchPaths:
      {CandidateAssemblyFiles}
      {HintPathFromItem}
      {TargetFrameworkDirectory}
      {Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}
      {AssemblyFolders}
      {GAC}
      {RawFileName}
      bin\Debug\
  AllowedAssemblyExtensions:
      .winmd
      .dll
      .exe
  AllowedRelatedFileExtensions:
      .pdb
      .xml
      .pri
  AppConfigFile:
      
  AutoUnify:
      True
  CopyLocalDependenciesWhenParentReferenceInGac:
      True
  FindDependencies:
      True
  TargetProcessorArchitecture:
      msil
  StateFile:
      obj\Debug\Selltis.Data.csprojResolveAssemblyReference.cache
  InstalledAssemblySubsetTables:
  IgnoreInstalledAssemblySubsetTable:
      False
  TargetFrameworkSubsets:
  FullTargetFrameworkSubsetNames:
      Full
  ProfileName:
      
  FullFrameworkFolders:
      C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\
  LatestTargetFrameworkDirectories:
  ProfileTablesLocation:
  Unified primary reference "mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ephtmltopdf.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Google.GData.Client.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\IMAP4.Net.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\POP3.Net.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\SMTP.Net.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\UltimateAjax.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll".
      Reference found at search path location "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Microsoft.CSharp, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Microsoft.VisualBasic, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.VisualBasic.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.VisualBasic.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\DocumentFormat.OpenXml.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "ephtmltopdf, Version=*******, Culture=neutral, PublicKeyToken=5b5f377bc08a4d32".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ephtmltopdf.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v2.0.50727".
  Primary reference "Google.GData.Client, Version=2.2.0.0, Culture=neutral, PublicKeyToken=04a59ca9b0273830".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Google.GData.Client.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v2.0.50727".
  Primary reference "IMAP4.Net, Version=3.0.1.0, Culture=neutral, PublicKeyToken=986a9aa31056680b".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\IMAP4.Net.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v2.0.50727".
  Primary reference "ImapUtility, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ImapUtility.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "MailBee.NET, Version=11.2.0.590, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\MailBee.NET.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Microsoft.WindowsAzure.Configuration, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.Configuration.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Microsoft.WindowsAzure.Storage, Version=8.1.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.Storage.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Microsoft.WindowsAzure.StorageClient, Version=1.7.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v2.0.50727".
  Primary reference "POP3.Net, Version=3.0.1.0, Culture=neutral, PublicKeyToken=986a9aa31056680b".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\POP3.Net.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v2.0.50727".
  Primary reference "PublicDomain, Version=0.2.50.0, Culture=neutral, PublicKeyToken=fd3f43b5776a962b".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v2.0.50727".
  Primary reference "Quickwebsoft.Web.UI.WebControls.EventCalendar, Version=2006.1.4.310, Culture=neutral, PublicKeyToken=ef7441958530bb36".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v2.0.50727".
  Primary reference "SautinSoft.Document, Version=4.5.6.17, Culture=neutral, PublicKeyToken=e759c76c7515592a".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\SautinSoft.Document.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "SMTP.Net, Version=3.0.1.0, Culture=neutral, PublicKeyToken=986a9aa31056680b".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\SMTP.Net.dll".
      Reference found at search path location "{HintPathFromItem}".
      Found related file "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\SMTP.Net.xml".
      The ImageRuntimeVersion for this reference is "v2.0.50727".
  Unified primary reference "System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ephtmltopdf.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Google.GData.Client.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\IMAP4.Net.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\POP3.Net.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\SMTP.Net.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\UltimateAjax.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ephtmltopdf.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\POP3.Net.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\SMTP.Net.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ephtmltopdf.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\UltimateAjax.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Runtime.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Runtime.Serialization.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Runtime.Serialization.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ephtmltopdf.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\SMTP.Net.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\UltimateAjax.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Web.ApplicationServices, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.ApplicationServices.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.ApplicationServices.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Extensions.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Extensions.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Web.Mvc, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll".
      Reference found at search path location "{HintPathFromItem}".
      Found related file "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.xml".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Xml, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ephtmltopdf.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Google.GData.Client.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Xml.Linq, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "3.5.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Data.DataSetExtensions, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Telerik.Windows.Documents.Core, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Documents.Core.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Telerik.Windows.Documents.Fixed, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Documents.Fixed.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Telerik.Windows.Documents.Flow, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Documents.Flow.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Telerik.Windows.Documents.Flow.FormatProviders.Pdf, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Telerik.Windows.Zip, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Zip.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "UltimateAjax, Version=1.1.2570.28740, Culture=neutral, PublicKeyToken=264b229a45ee0694".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\UltimateAjax.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v2.0.50727".
  Primary reference "WindowsBase, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\WindowsBase.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\WindowsBase.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Core, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "3.5.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified Dependency "System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ephtmltopdf.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Windows.Forms.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Windows.Forms.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Windows.Forms.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.winmd", but it didn't exist.
      Required by "Quickwebsoft.Web.UI.WebControls.EventCalendar".
      Required by "MailBee.NET".
      Required by "ephtmltopdf, Version=*******, Culture=neutral, PublicKeyToken=5b5f377bc08a4d32, processorArchitecture=MSIL".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified Dependency "Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed".
      Using this version instead of original version "4.0.5.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Google.GData.Client.dll" because AutoUnify is 'true'.
      Could not resolve this reference. Could not locate the assembly "Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Newtonsoft.Json.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Newtonsoft.Json.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Newtonsoft.Json.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Newtonsoft.Json.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Newtonsoft.Json.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Newtonsoft.Json.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Newtonsoft.Json.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Newtonsoft.Json.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Newtonsoft.Json.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Newtonsoft.Json.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Newtonsoft.Json.dll", but its name "Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed" didn't match.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Newtonsoft.Json.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Newtonsoft.Json.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Newtonsoft.Json.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Newtonsoft.Json.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Newtonsoft.Json.winmd", but it didn't exist.
          Considered "bin\Debug\Newtonsoft.Json.dll", but it didn't exist.
          Considered "bin\Debug\Newtonsoft.Json.exe", but it didn't exist.
      Required by "Google.GData.Client".
      Required by "Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL".
  Dependency "MailBee.NET, Version=12.2.0.630, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1".
      Could not resolve this reference. Could not locate the assembly "MailBee.NET, Version=12.2.0.630, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\MailBee.NET.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\MailBee.NET.dll", but its name "MailBee.NET, Version=11.2.0.590, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1" didn't match.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\MailBee.NET.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\MailBee.NET.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\MailBee.NET.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\MailBee.NET.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\MailBee.NET.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\MailBee.NET.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\MailBee.NET.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\MailBee.NET.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\MailBee.NET.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\MailBee.NET.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\MailBee.NET.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\MailBee.NET.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\MailBee.NET.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "MailBee.NET, Version=12.2.0.630, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\MailBee.NET.winmd", but it didn't exist.
          Considered "bin\Debug\MailBee.NET.dll", but its name "MailBee.NET, Version=11.2.0.590, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1" didn't match.
          Considered "bin\Debug\MailBee.NET.exe", but it didn't exist.
      Required by "ImapUtility".
  Dependency "System.Security, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Security.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Security.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Security.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Security.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Security.winmd", but it didn't exist.
      Required by "Telerik.Windows.Documents.Fixed".
      Required by "MailBee.NET".
      Required by "Telerik.Windows.Documents.Flow.FormatProviders.Pdf".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "itextsharp, Version=4.1.6.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca".
      Could not resolve this reference. Could not locate the assembly "itextsharp, Version=4.1.6.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\itextsharp.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\itextsharp.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\itextsharp.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\itextsharp.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\itextsharp.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\itextsharp.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\itextsharp.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\itextsharp.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\itextsharp.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\itextsharp.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\itextsharp.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\itextsharp.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\itextsharp.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\itextsharp.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\itextsharp.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "itextsharp, Version=4.1.6.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\itextsharp.winmd", but it didn't exist.
          Considered "bin\Debug\itextsharp.dll", but it didn't exist.
          Considered "bin\Debug\itextsharp.exe", but it didn't exist.
      Required by "MailBee.NET".
  Dependency "HtmlAgilityPack, Version=1.4.9.5, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a".
      Could not resolve this reference. Could not locate the assembly "HtmlAgilityPack, Version=1.4.9.5, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\HtmlAgilityPack.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\HtmlAgilityPack.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\HtmlAgilityPack.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\HtmlAgilityPack.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\HtmlAgilityPack.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\HtmlAgilityPack.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\HtmlAgilityPack.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\HtmlAgilityPack.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\HtmlAgilityPack.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\HtmlAgilityPack.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\HtmlAgilityPack.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\HtmlAgilityPack.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\HtmlAgilityPack.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\HtmlAgilityPack.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\HtmlAgilityPack.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "HtmlAgilityPack, Version=1.4.9.5, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\HtmlAgilityPack.winmd", but it didn't exist.
          Considered "bin\Debug\HtmlAgilityPack.dll", but it didn't exist.
          Considered "bin\Debug\HtmlAgilityPack.exe", but it didn't exist.
      Required by "MailBee.NET".
  Dependency "Microsoft.Exchange.WebServices, Version=15.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "Microsoft.Exchange.WebServices, Version=15.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Exchange.WebServices.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Exchange.WebServices.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Exchange.WebServices.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Exchange.WebServices.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Exchange.WebServices.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Exchange.WebServices.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Exchange.WebServices.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Exchange.WebServices.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Exchange.WebServices.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Exchange.WebServices.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Exchange.WebServices.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Exchange.WebServices.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Exchange.WebServices.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Exchange.WebServices.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Exchange.WebServices.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Microsoft.Exchange.WebServices, Version=15.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Microsoft.Exchange.WebServices.winmd", but it didn't exist.
          Considered "bin\Debug\Microsoft.Exchange.WebServices.dll", but it didn't exist.
          Considered "bin\Debug\Microsoft.Exchange.WebServices.exe", but it didn't exist.
      Required by "MailBee.NET".
  Unified Dependency "System.Management, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Management.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Management.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Management.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Management.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Management.winmd", but it didn't exist.
      Required by "PublicDomain".
      Required by "MailBee.NET".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "Microsoft.Data.Services.Client, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "Microsoft.Data.Services.Client, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.Services.Client.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.Services.Client.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.Services.Client.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.Services.Client.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.Services.Client.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.Services.Client.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.Services.Client.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.Services.Client.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.Services.Client.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.Services.Client.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.Services.Client.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.Services.Client.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.Services.Client.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.Services.Client.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.Services.Client.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Microsoft.Data.Services.Client, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Microsoft.Data.Services.Client.winmd", but it didn't exist.
          Considered "bin\Debug\Microsoft.Data.Services.Client.dll", but it didn't exist.
          Considered "bin\Debug\Microsoft.Data.Services.Client.exe", but it didn't exist.
      Required by "Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL".
  Dependency "Microsoft.Data.OData, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "Microsoft.Data.OData, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.OData.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.OData.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.OData.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.OData.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.OData.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.OData.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.OData.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.OData.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.OData.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.OData.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.OData.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.OData.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.OData.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.OData.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.OData.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Microsoft.Data.OData, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Microsoft.Data.OData.winmd", but it didn't exist.
          Considered "bin\Debug\Microsoft.Data.OData.dll", but it didn't exist.
          Considered "bin\Debug\Microsoft.Data.OData.exe", but it didn't exist.
      Required by "Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL".
  Dependency "Microsoft.Data.Edm, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "Microsoft.Data.Edm, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.Edm.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.Edm.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.Edm.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.Edm.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.Edm.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.Edm.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.Edm.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.Edm.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.Edm.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.Edm.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.Edm.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.Edm.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.Edm.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.Edm.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.Edm.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Microsoft.Data.Edm, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Microsoft.Data.Edm.winmd", but it didn't exist.
          Considered "bin\Debug\Microsoft.Data.Edm.dll", but it didn't exist.
          Considered "bin\Debug\Microsoft.Data.Edm.exe", but it didn't exist.
      Required by "Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL".
  Dependency "Microsoft.Azure.KeyVault.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "Microsoft.Azure.KeyVault.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Azure.KeyVault.Core.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Azure.KeyVault.Core.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Azure.KeyVault.Core.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Azure.KeyVault.Core.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Azure.KeyVault.Core.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Azure.KeyVault.Core.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Azure.KeyVault.Core.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Azure.KeyVault.Core.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Azure.KeyVault.Core.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Azure.KeyVault.Core.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Azure.KeyVault.Core.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Azure.KeyVault.Core.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Azure.KeyVault.Core.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Azure.KeyVault.Core.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Azure.KeyVault.Core.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Microsoft.Azure.KeyVault.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Microsoft.Azure.KeyVault.Core.winmd", but it didn't exist.
          Considered "bin\Debug\Microsoft.Azure.KeyVault.Core.dll", but it didn't exist.
          Considered "bin\Debug\Microsoft.Azure.KeyVault.Core.exe", but it didn't exist.
      Required by "Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL".
  Unified Dependency "System.Data.Services.Client, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "3.5.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Services.Client.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Data.Services.Client.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Data.Services.Client.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Data.Services.Client.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Services.Client.winmd", but it didn't exist.
      Required by "Microsoft.WindowsAzure.StorageClient, Version=1.7.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified Dependency "System.Transactions, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Transactions.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Transactions.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Transactions.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Transactions.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Transactions.winmd", but it didn't exist.
      Required by "PublicDomain".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified Dependency "System.Design, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Design.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Design.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Design.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Design.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Design.winmd", but it didn't exist.
      Required by "Quickwebsoft.Web.UI.WebControls.EventCalendar".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "PresentationCore, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\PresentationCore.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\PresentationCore.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\PresentationCore.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\PresentationCore.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\PresentationCore.winmd", but it didn't exist.
      Required by "Telerik.Windows.Documents.Flow".
      Required by "Telerik.Windows.Documents.Core".
      Required by "SautinSoft.Document".
      Required by "Telerik.Windows.Documents.Fixed".
      Required by "Telerik.Windows.Documents.Flow.FormatProviders.Pdf".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "PresentationFramework, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\PresentationFramework.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\PresentationFramework.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\PresentationFramework.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\PresentationFramework.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\PresentationFramework.winmd", but it didn't exist.
      Required by "Telerik.Windows.Documents.Flow".
      Required by "Telerik.Windows.Documents.Core".
      Required by "SautinSoft.Document".
      Required by "Telerik.Windows.Documents.Fixed".
      Required by "Telerik.Windows.Documents.Flow.FormatProviders.Pdf".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "System.ComponentModel.DataAnnotations, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.ComponentModel.DataAnnotations.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.ComponentModel.DataAnnotations.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.ComponentModel.DataAnnotations.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.ComponentModel.DataAnnotations.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.ComponentModel.DataAnnotations.winmd", but it didn't exist.
      Required by "System.Web.Mvc".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Razor.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Razor.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Razor.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Razor.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Razor.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.Razor.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.Razor.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.Razor.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.Razor.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.Razor.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.Razor.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\System.Web.Razor.winmd", but it didn't exist.
          Considered "bin\Debug\System.Web.Razor.dll", but it didn't exist.
          Considered "bin\Debug\System.Web.Razor.exe", but it didn't exist.
      Required by "System.Web.Mvc".
  Dependency "System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.WebPages.Razor.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.WebPages.Razor.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.WebPages.Razor.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.WebPages.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.WebPages.Razor.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.WebPages.Razor.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.WebPages.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.WebPages.Razor.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.WebPages.Razor.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.WebPages.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.WebPages.Razor.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.WebPages.Razor.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.WebPages.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.WebPages.Razor.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.WebPages.Razor.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\System.Web.WebPages.Razor.winmd", but it didn't exist.
          Considered "bin\Debug\System.Web.WebPages.Razor.dll", but it didn't exist.
          Considered "bin\Debug\System.Web.WebPages.Razor.exe", but it didn't exist.
      Required by "System.Web.Mvc".
  Dependency "System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.WebPages.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.WebPages.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.WebPages.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.WebPages.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.WebPages.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.WebPages.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.WebPages.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.WebPages.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.WebPages.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.WebPages.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.WebPages.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.WebPages.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.WebPages.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.WebPages.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.WebPages.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\System.Web.WebPages.winmd", but it didn't exist.
          Considered "bin\Debug\System.Web.WebPages.dll", but it didn't exist.
          Considered "bin\Debug\System.Web.WebPages.exe", but it didn't exist.
      Required by "System.Web.Mvc".
  Dependency "System.Runtime.Caching, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Runtime.Caching.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Runtime.Caching.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Runtime.Caching.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Runtime.Caching.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Runtime.Caching.winmd", but it didn't exist.
      Required by "System.Web.Mvc".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\Microsoft.Web.Infrastructure.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\Microsoft.Web.Infrastructure.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\Microsoft.Web.Infrastructure.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Web.Infrastructure.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Web.Infrastructure.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Web.Infrastructure.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Web.Infrastructure.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Web.Infrastructure.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Web.Infrastructure.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Web.Infrastructure.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Web.Infrastructure.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Web.Infrastructure.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Web.Infrastructure.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Web.Infrastructure.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Web.Infrastructure.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Microsoft.Web.Infrastructure.winmd", but it didn't exist.
          Considered "bin\Debug\Microsoft.Web.Infrastructure.dll", but it didn't exist.
          Considered "bin\Debug\Microsoft.Web.Infrastructure.exe", but it didn't exist.
      Required by "System.Web.Mvc".
  Dependency "System.Data.Linq, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Linq.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Data.Linq.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Data.Linq.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Data.Linq.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Linq.winmd", but it didn't exist.
      Required by "System.Web.Mvc".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Entity.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Data.Entity.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Data.Entity.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Data.Entity.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Entity.winmd", but it didn't exist.
      Required by "System.Web.Mvc".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "System.Xaml, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xaml.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\System.Xaml.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\System.Xaml.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\System.Xaml.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xaml.winmd", but it didn't exist.
      Required by "Telerik.Windows.Documents.Core".
      Required by "Telerik.Windows.Documents.Fixed".
      Required by "Telerik.Windows.Documents.Flow".
      Required by "Telerik.Windows.Documents.Flow.FormatProviders.Pdf".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  There was a conflict between "MailBee.NET, Version=11.2.0.590, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1" and "MailBee.NET, Version=12.2.0.630, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1".
      "MailBee.NET, Version=11.2.0.590, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1" was chosen because it was primary and "MailBee.NET, Version=12.2.0.630, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1" was not.
      References which depend on "MailBee.NET, Version=11.2.0.590, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1" [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\MailBee.NET.dll].
          C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\MailBee.NET.dll
            Project file item includes which caused reference "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\MailBee.NET.dll".
              MailBee.NET
      References which depend on "MailBee.NET, Version=12.2.0.630, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1" [].
          C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ImapUtility.dll
            Project file item includes which caused reference "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ImapUtility.dll".
              ImapUtility
C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets(1605,5): warning MSB3247: Found conflicts between different versions of the same dependent assembly. [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
  AssemblyFoldersEx location: "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}"
          C:\Program Files (x86)\Microsoft.NET\Primary Interop Assemblies\
          C:\Program Files (x86)\Common Files\Microsoft Shared\MSEnv\PublicAssemblies
          C:\Program Files (x86)\Microsoft Chart Controls\Assemblies
          C:\Program Files\Microsoft SQL Server\150\DTS\Tasks
          C:\Program Files\Microsoft SQL Server\150\DTS\PipelineComponents\
          C:\Program Files\Microsoft SQL Server\150\DTS\ForEachEnumerators
          C:\Program Files\Microsoft SQL Server\150\DTS\Connections\
Done executing task "ResolveAssemblyReference".
Done building target "ResolveAssemblyReferences" in project "Selltis.Data.csproj".
Target "ResolveComReferences" skipped, due to false condition; ('@(COMReference)'!='' or '@(COMFileReference)'!='') was evaluated as (''!='' or ''!='').
Target "AfterResolveReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "ResolveReferences" depends on it):
Done building target "AfterResolveReferences" in project "Selltis.Data.csproj".
Target "GetReferenceAssemblyPaths" skipped. Previously built successfully.
Target "ImplicitlyExpandDesignTimeFacades" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.NETFramework.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "ResolveReferences" depends on it):
Task "Message" skipped, due to false condition; ('%(ReferencePath.ResolvedFrom)' == 'ImplicitlyExpandDesignTimeFacades') was evaluated as ('{HintPathFromItem}' == 'ImplicitlyExpandDesignTimeFacades').
Task "Message" skipped, due to false condition; ('%(ReferencePath.ResolvedFrom)' == 'ImplicitlyExpandDesignTimeFacades') was evaluated as ('{TargetFrameworkDirectory}' == 'ImplicitlyExpandDesignTimeFacades').
Task "Message" skipped, due to false condition; ('%(ReferencePath.ResolvedFrom)' == 'ImplicitlyExpandDesignTimeFacades') was evaluated as ('C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll' == 'ImplicitlyExpandDesignTimeFacades').
Done building target "ImplicitlyExpandDesignTimeFacades" in project "Selltis.Data.csproj".
Target "ResolveReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "CoreBuild" depends on it):
Done building target "ResolveReferences" in project "Selltis.Data.csproj".
Target "ValidationExtension" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.WorkflowBuildExtensions.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PrepareResources" depends on it):
Task "WorkflowBuildMessageTask" skipped, due to false condition; ('$(SkipWorkflowValidation)'!='' and '$(SkipWorkflowValidation)'!='true' and '$(SkipWorkflowValidation)'!='false') was evaluated as (''!='' and ''!='true' and ''!='false').
Done building target "ValidationExtension" in project "Selltis.Data.csproj".
Target "ExpressionBuildExtension" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.WorkflowBuildExtensions.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PrepareResources" depends on it):
Task "WorkflowBuildMessageTask" skipped, due to false condition; ('$(DisableWorkflowCompiledExpressions)'!='' and '$(DisableWorkflowCompiledExpressions)'!='true' and '$(DisableWorkflowCompiledExpressions)'!='false') was evaluated as (''!='' and ''!='true' and ''!='false').
Done building target "ExpressionBuildExtension" in project "Selltis.Data.csproj".
Target "XamlMarkupCompilePass1" skipped, due to false condition; ('@(XamlPage)' != '' or '@(XamlAppDef)' != '') was evaluated as ('' != '' or '' != '').
Target "XamlMarkupCompileReadGeneratedFileList" skipped, due to false condition; ('@(XamlPage)' != '' or '@(XamlAppDef)' != '') was evaluated as ('' != '' or '' != '').
Target "XamlMarkupCompileAddFilesGenerated" skipped, due to false condition; ('@(XamlPage)' != '' or '@(XamlAppDef)' != '') was evaluated as ('' != '' or '' != '').
Target "XamlMarkupCompilePass2" skipped, due to false condition; ('$(XamlRequiresCompilationPass2)' == 'true' ) was evaluated as ('false' == 'true' ).
Target "XamlMarkupCompileReadPass2Flag" skipped, due to false condition; ('@(XamlPage)' != '' or '@(XamlAppDef)' != '') was evaluated as ('' != '' or '' != '').
Target "XamlMarkupCompileAddExtensionFilesGenerated" skipped, due to false condition; ('@(XamlPage)' != '' or '@(XamlAppDef)' != '') was evaluated as ('' != '' or '' != '').
Target "AddDeferredValidationErrorsFileToFileWrites" skipped, due to false condition; (Exists('$(DeferredValidationErrorsFileName)')) was evaluated as (Exists('obj\Debug\\AC2C1ABA-CCF6-44D4-8127-588FD4D0A860-DeferredValidationErrors.xml')).
Target "ReportValidationBuildExtensionErrors" skipped, due to false condition; ('$(SkipWorkflowValidation)' != 'true' and ('@(XamlPage)' != '' or '@(XamlAppDef)' != '')) was evaluated as ('' != 'true' and ('' != '' or '' != '')).
Target "MarkupCompilePass1" skipped, due to false condition; ('@(Page)' != '' or '@(ApplicationDefinition)' != '' ) was evaluated as ('' != '' or '' != '' ).
Target "AfterMarkupCompilePass1" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.WinFX.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PrepareResources" depends on it):
Done building target "AfterMarkupCompilePass1" in project "Selltis.Data.csproj".
Target "MarkupCompilePass2ForMainAssembly" skipped, due to false condition; ('$(_RequireMCPass2ForMainAssembly)' == 'true' ) was evaluated as ('false' == 'true' ).
Target "FileClassification" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.WinFX.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PrepareResources" depends on it):
Task "FileClassifier" skipped, due to false condition; ('@(GeneratedBaml)' != '' or '@(Resource)' != '' or '@(Font)' != '') was evaluated as ('' != '' or '' != '' or '' != '').
Task "Message" skipped, due to false condition; ('$(MSBuildTargetsVerbose)'=='true') was evaluated as (''=='true').
Task "Message" skipped, due to false condition; ('$(MSBuildTargetsVerbose)'=='true') was evaluated as (''=='true').
Done building target "FileClassification" in project "Selltis.Data.csproj".
Target "MainResourcesGeneration" skipped, due to false condition; ('@(MainEmbeddedFiles)' != '') was evaluated as ('' != '').
Target "AssignWinFXEmbeddedResource" skipped, due to false condition; ('@(WinFXEmbeddedResource)' != '') was evaluated as ('' != '').
Target "AssignTargetPaths" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PrepareResourceNames" depends on it):
Using "AssignTargetPath" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "AssignTargetPath"
Done executing task "AssignTargetPath".
Task "AssignTargetPath"
Done executing task "AssignTargetPath".
Task "AssignTargetPath"
Done executing task "AssignTargetPath".
Task "AssignTargetPath"
Done executing task "AssignTargetPath".
Task "AssignTargetPath" skipped, due to false condition; ('@(_DeploymentBaseManifestWithTargetPath)'=='' and '%(None.Extension)'=='.manifest') was evaluated as (''=='' and '.config'=='.manifest').
Task "AssignTargetPath" skipped, due to false condition; ('@(_DeploymentBaseManifestWithTargetPath)'=='' and '%(None.Extension)'=='.manifest') was evaluated as (''=='' and '.settings'=='.manifest').
Done building target "AssignTargetPaths" in project "Selltis.Data.csproj".
Target "AssignTargetPaths" skipped. Previously built successfully.
Target "SplitResourcesByCulture" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PrepareResourceNames" depends on it):
Task "Warning" skipped, due to false condition; ('@(ResxWithNoCulture)'!='') was evaluated as (''!='').
Task "Warning" skipped, due to false condition; ('@(ResxWithCulture)'!='') was evaluated as (''!='').
Task "Warning" skipped, due to false condition; ('@(NonResxWithCulture)'!='') was evaluated as (''!='').
Task "Warning" skipped, due to false condition; ('@(NonResxWithNoCulture)'!='') was evaluated as (''!='').
Using "AssignCulture" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "AssignCulture"
  Culture of "" was assigned to file "Properties\Resources.resx".
Done executing task "AssignCulture".
Done building target "SplitResourcesByCulture" in project "Selltis.Data.csproj".
Target "CreateManifestResourceNames" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.CSharp.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PrepareResourceNames" depends on it):
Using "CreateCSharpManifestResourceName" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "CreateCSharpManifestResourceName"
  Root namespace is 'Selltis.BusinessLogic'.
  Resource file 'Properties\Resources.resx' doesn't depend on any other file.
  Resource file 'Properties\Resources.resx' gets manifest resource name 'Selltis.BusinessLogic.Properties.Resources'.
Done executing task "CreateCSharpManifestResourceName".
Task "CreateCSharpManifestResourceName" skipped, due to false condition; ('%(EmbeddedResource.ManifestResourceName)' == '' and '%(EmbeddedResource.WithCulture)' == 'true' and '%(EmbeddedResource.Type)' == 'Non-Resx') was evaluated as ('' == '' and 'false' == 'true' and 'Resx' == 'Non-Resx').
Done building target "CreateManifestResourceNames" in project "Selltis.Data.csproj".
Target "CreateCustomManifestResourceNames" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PrepareResourceNames" depends on it):
Done building target "CreateCustomManifestResourceNames" in project "Selltis.Data.csproj".
Target "PrepareResourceNames" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PrepareResources" depends on it):
Done building target "PrepareResourceNames" in project "Selltis.Data.csproj".
Target "ResolveAssemblyReferences" skipped. Previously built successfully.
Target "SplitResourcesByCulture" skipped. Previously built successfully.
Target "BeforeResGen" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "ResGen" depends on it):
Done building target "BeforeResGen" in project "Selltis.Data.csproj".
Target "CoreResGen" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "ResGen" depends on it):
Using "GenerateResource" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "GenerateResource"
  No resources are out of date with respect to their source files. Skipping resource generation.
Done executing task "GenerateResource".
Task "GenerateResource" skipped, due to false condition; ('%(EmbeddedResource.Type)' == 'Resx' and '%(EmbeddedResource.GenerateResource)' != 'false' and '$(GenerateResourceMSBuildRuntime)' == 'CLR2') was evaluated as ('Resx' == 'Resx' and '' != 'false' and 'CurrentRuntime' == 'CLR2').
Done building target "CoreResGen" in project "Selltis.Data.csproj".
Target "AfterResGen" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "ResGen" depends on it):
Done building target "AfterResGen" in project "Selltis.Data.csproj".
Target "ResGen" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PrepareResources" depends on it):
Done building target "ResGen" in project "Selltis.Data.csproj".
Target "CompileLicxFiles" skipped, due to false condition; ('@(_LicxFile)'!='') was evaluated as (''!='').
Target "PrepareResources" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "CoreBuild" depends on it):
Done building target "PrepareResources" in project "Selltis.Data.csproj".
Target "ResolveKeySource" skipped, due to false condition; ($(SignManifests) == 'true' or $(SignAssembly) == 'true') was evaluated as ( == 'true' or  == 'true').
Target "ResolveReferences" skipped. Previously built successfully.
Target "ResolveKeySource" skipped, due to false condition; ($(SignManifests) == 'true' or $(SignAssembly) == 'true') was evaluated as ( == 'true' or  == 'true').
Target "ResolveComReferences" skipped, due to false condition; ('@(COMReference)'!='' or '@(COMFileReference)'!='') was evaluated as (''!='' or ''!='').
Target "ResolveNativeReferences" skipped, due to false condition; ('@(NativeReference)'!='') was evaluated as (''!='').
Target "_SetExternalWin32ManifestProperties" skipped, due to false condition; ('$(GenerateClickOnceManifests)'=='true' or '@(NativeReference)'!='' or '@(ResolvedIsolatedComModules)'!='') was evaluated as (''=='true' or ''!='' or ''!='').
Target "_SetEmbeddedWin32ManifestProperties" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "SetWin32ManifestProperties" depends on it):
Using "GetFrameworkPath" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "GetFrameworkPath"
Done executing task "GetFrameworkPath".
Done building target "_SetEmbeddedWin32ManifestProperties" in project "Selltis.Data.csproj".
Target "SetWin32ManifestProperties" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "Compile" depends on it):
Done building target "SetWin32ManifestProperties" in project "Selltis.Data.csproj".
Target "_GenerateCompileInputs" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "Compile" depends on it):
Task "Warning" skipped, due to false condition; ('@(ManifestResourceWithNoCulture)'!='' and '%(ManifestResourceWithNoCulture.EmittedForCompatibilityOnly)'=='') was evaluated as ('obj\Debug\Selltis.BusinessLogic.Properties.Resources.resources'!='' and 'true'=='').
Task "Warning" skipped, due to false condition; ('@(ManifestNonResxWithNoCultureOnDisk)'!='' and '%(ManifestNonResxWithNoCultureOnDisk.EmittedForCompatibilityOnly)'=='') was evaluated as (''!='' and ''=='').
Done building target "_GenerateCompileInputs" in project "Selltis.Data.csproj".
Target "PrepareForBuild" skipped. Previously built successfully.
Target "GetReferenceAssemblyPaths" skipped. Previously built successfully.
Target "_SetTargetFrameworkMonikerAttribute" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.CSharp.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "GenerateTargetFrameworkMonikerAttribute" depends on it):
Done building target "_SetTargetFrameworkMonikerAttribute" in project "Selltis.Data.csproj".
Target "GenerateTargetFrameworkMonikerAttribute" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "BeforeCompile" depends on it):
Skipping target "GenerateTargetFrameworkMonikerAttribute" because all output files are up-to-date with respect to the input files.
Input files: C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets
Output files: C:\Users\<USER>\AppData\Local\Temp\.NETFramework,Version=v4.8.AssemblyAttributes.cs
Done building target "GenerateTargetFrameworkMonikerAttribute" in project "Selltis.Data.csproj".
Target "GenerateAdditionalSources" skipped, due to false condition; ('@(AssemblyAttributes)' != '' and '$(GenerateAdditionalSources)' == 'true') was evaluated as ('' != '' and '' == 'true').
Target "BeforeCompile" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "Compile" depends on it):
Done building target "BeforeCompile" in project "Selltis.Data.csproj".
Target "_TimeStampBeforeCompile" skipped, due to false condition; ('$(RunPostBuildEvent)'=='OnOutputUpdated' or ('$(RegisterForComInterop)'=='true' and '$(OutputType)'=='library')) was evaluated as (''=='OnOutputUpdated' or (''=='true' and 'Library'=='library')).
Target "_ComputeNonExistentFileProperty" skipped, due to false condition; (('$(BuildingInsideVisualStudio)' == 'true') and ('$(BuildingOutOfProcess)' != 'true') and (('$(BuildingProject)' == 'false') or ('$(UseHostCompilerIfAvailable)' == 'true'))) was evaluated as (('' == 'true') and ('' != 'true') and (('true' == 'false') or ('true' == 'true'))).
Target "PreXsdCodeGen" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.ServiceModel.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "CoreCompile" depends on it):
Using "CallTarget" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "CallTarget"
Target "CleanXsdCodeGen" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.ServiceModel.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "PreXsdCodeGen" depends on it):
Using "Delete" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "Delete"
Done executing task "Delete".
Done building target "CleanXsdCodeGen" in project "Selltis.Data.csproj".
Done executing task "CallTarget".
Done building target "PreXsdCodeGen" in project "Selltis.Data.csproj".
Target "XsdCodeGen" skipped, due to false condition; ( '$(XsdCodeGenPreCondition)' == 'True' ) was evaluated as ( 'False' == 'True' ).
Target "CoreCompile" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.CSharp.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "Compile" depends on it):
Building target "CoreCompile" completely.
Input file "C:\Users\<USER>\AppData\Local\Temp\.NETFramework,Version=v4.8.AssemblyAttributes.cs" is newer than output file "Selltis.BusinessLogic.xml".
Using "Csc" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "Csc"
  C:\Windows\Microsoft.NET\Framework\v4.0.30319\Csc.exe /noconfig /checked+ /nowarn:1701,1702 /nostdlib+ /warn:4 /doc:Selltis.BusinessLogic.xml /define:TRACE;DEBUG;Windows /highentropyva+ /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\DocumentFormat.OpenXml.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ephtmltopdf.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Google.GData.Client.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\IMAP4.Net.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\ImapUtility.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\MailBee.NET.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.VisualBasic.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.Configuration.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.Storage.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\POP3.Net.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\SautinSoft.Document.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\SMTP.Net.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Runtime.Serialization.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.ApplicationServices.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Extensions.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Documents.Core.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Documents.Fixed.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Documents.Flow.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Zip.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\UltimateAjax.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\WindowsBase.dll" /debug+ /debug:full /filealign:512 /out:obj\Debug\Selltis.BusinessLogic.dll /subsystemversion:6.00 /resource:obj\Debug\Selltis.BusinessLogic.Properties.Resources.resources /target:library /utf8output Properties\Resources.Designer.cs TangibleNumericHelper.cs TangibleDateHelper.cs clArray.cs clAttachments.cs clAuto.cs clAutomatorMenu.cs clAzureFileStorage.cs clC.cs clData.cs clDefaults.cs clDiaAdmPerm.cs clDiaFormPro.cs clEmail.cs clEmailAlerts.cs clEncryption.cs clError.cs clFileBrowse.cs clGoogleAuth.cs clHistory.cs clHistoryItem.cs clImport.cs clImportTasks.cs clInfoMessage.cs clInit.cs clList.cs clLog.cs clLogic.cs clMembershipData.cs clMetaData.cs clNumMask.cs clPDF.cs clPerm.cs clProject.cs clRowSet.cs clSchema.cs clScriptsRowSet.cs clScrMngRowSet.cs clSelltisMembershipProvider.cs clSelltisMembershipProviderNew.cs clSelltisMobileBase.cs clSend.cs clSettings.cs clSQL.cs clTable.cs clTransform.cs ClUI.cs clUtil.cs clVar.cs clWebForm.cs clWebMail.cs clWorkareaMessage.cs cus_clScriptsCustom.cs cus_clScriptsRowSetCustom.cs cus_clSelltisMobileCustom.cs EfcOnlineApi.cs Properties\AssemblyInfo.cs SelltisCache.cs WordToPDF.cs "C:\Users\<USER>\AppData\Local\Temp\.NETFramework,Version=v4.8.AssemblyAttributes.cs"
  Microsoft (R) Visual C# Compiler version 4.8.9232.0
  
  for C# 5
  Copyright (C) Microsoft Corporation. All rights reserved.
  
  
  
  This compiler is provided as part of the Microsoft (R) .NET Framework, but only supports language versions up to C# 5, which is no longer the latest version. For compilers that support newer versions of the C# programming language, see http://go.microsoft.com/fwlink/?LinkID=533240
  
clSQL.cs(3242,35): error CS1056: Unexpected character '$' [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
clSQL.cs(18,7): error CS1041: Identifier expected; 'static' is a keyword [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
clSQL.cs(18,14): error CS1518: Expected class, delegate, enum, interface, or struct [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
clTransform.cs(36,7): error CS1041: Identifier expected; 'static' is a keyword [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
clTransform.cs(36,14): error CS1518: Expected class, delegate, enum, interface, or struct [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
ClUI.cs(3,7): error CS1041: Identifier expected; 'static' is a keyword [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
ClUI.cs(3,14): error CS1518: Expected class, delegate, enum, interface, or struct [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
  The command exited with code 1.
Done executing task "Csc" -- FAILED.
Done building target "CoreCompile" in project "Selltis.Data.csproj" -- FAILED.
Target "_CheckForCompileOutputs" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "_CleanGetCurrentAndPriorFileWrites" depends on it):
Done building target "_CheckForCompileOutputs" in project "Selltis.Data.csproj".
Target "_SGenCheckForOutputs" skipped, due to false condition; ('$(_SGenGenerateSerializationAssembliesConfig)' == 'On' or ('@(WebReferenceUrl)'!='' and '$(_SGenGenerateSerializationAssembliesConfig)' == 'Auto')) was evaluated as ('Off' == 'On' or (''!='' and 'Off' == 'Auto')).
Target "_CleanGetCurrentAndPriorFileWrites" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "_CleanRecordFileWrites" depends on it):
Using "ReadLinesFromFile" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "ReadLinesFromFile"
Done executing task "ReadLinesFromFile".
Using "ConvertToAbsolutePath" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "ConvertToAbsolutePath"
Done executing task "ConvertToAbsolutePath".
Using "FindUnderPath" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "FindUnderPath"
  Comparison path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic".
Done executing task "FindUnderPath".
Task "FindUnderPath"
  Comparison path is "bin\Debug\".
Done executing task "FindUnderPath".
Task "FindUnderPath"
  Comparison path is "obj\Debug\".
Done executing task "FindUnderPath".
Using "RemoveDuplicates" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "RemoveDuplicates"
Done executing task "RemoveDuplicates".
Done building target "_CleanGetCurrentAndPriorFileWrites" in project "Selltis.Data.csproj".
Target "_CleanRecordFileWrites" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (target "CoreBuild" depends on it):
Task "RemoveDuplicates"
Done executing task "RemoveDuplicates".
Task "MakeDir"
Done executing task "MakeDir".
Using "WriteLinesToFile" task from assembly "Microsoft.Build.Tasks.v4.0, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
Task "WriteLinesToFile"
Done executing task "WriteLinesToFile".
Done building target "_CleanRecordFileWrites" in project "Selltis.Data.csproj".
Done Building Project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (default targets) -- FAILED.
Overriding target "GetFrameworkPaths" in project "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" with target "GetFrameworkPaths" from project "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.NETFramework.targets".
Overriding target "SatelliteDllsProjectOutputGroup" in project "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" with target "SatelliteDllsProjectOutputGroup" from project "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.WinFX.targets".
Project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (1) is building "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (3) on node 1 (default targets).
Building with tools version "12.0".
Project file contains ToolsVersion="12.0". This toolset may be unknown or missing, in which case you may be able to resolve this by installing the appropriate version of MSBuild, or the build may have been forced to a particular ToolsVersion for policy reasons. Treating the project as if it had ToolsVersion="4.0". For more information, please see http://go.microsoft.com/fwlink/?LinkId=291333.
Target "_CheckForInvalidConfigurationAndPlatform" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (entry point):
Task "Error" skipped, due to false condition; ( '$(_InvalidConfigurationError)' == 'true' ) was evaluated as ( '' == 'true' ).
Task "Warning" skipped, due to false condition; ( '$(_InvalidConfigurationWarning)' == 'true' ) was evaluated as ( '' == 'true' ).
Task "Message"
  Configuration=Debug
Done executing task "Message".
Task "Message"
  Platform=AnyCPU
Done executing task "Message".
Task "Error" skipped, due to false condition; ('$(OutDir)' != '' and !HasTrailingSlash('$(OutDir)')) was evaluated as ('bin\Debug\' != '' and !HasTrailingSlash('bin\Debug\')).
Task "Error" skipped, due to false condition; ('$(BaseIntermediateOutputPath)' != '' and !HasTrailingSlash('$(BaseIntermediateOutputPath)')) was evaluated as ('obj\' != '' and !HasTrailingSlash('obj\')).
Task "Error" skipped, due to false condition; ('$(IntermediateOutputPath)' != '' and !HasTrailingSlash('$(IntermediateOutputPath)')) was evaluated as ('obj\Debug\' != '' and !HasTrailingSlash('obj\Debug\')).
Done building target "_CheckForInvalidConfigurationAndPlatform" in project "Selltis.Core.csproj".
Target "EntityDeploy" skipped, due to false condition; ('@(EntityDeploy)' != '') was evaluated as ('' != '').
Target "BeforeBuild" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "Build" depends on it):
Done building target "BeforeBuild" in project "Selltis.Core.csproj".
Target "BuildOnlySettings" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "CoreBuild" depends on it):
Done building target "BuildOnlySettings" in project "Selltis.Core.csproj".
Target "GetFrameworkPaths" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.NETFramework.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PrepareForBuild" depends on it):
Done building target "GetFrameworkPaths" in project "Selltis.Core.csproj".
Target "GetWinFXPath" skipped, due to false condition; (('@(Page)' != '' or '@(ApplicationDefinition)' != '' or '@(Resource)' != '') and ('$(GetWinFXNativePath)' != '' or '$(GetWinFXWoWPath)' != '' )) was evaluated as (('' != '' or '' != '' or '' != '') and ('' != '' or '' != '' )).
Target "GetReferenceAssemblyPaths" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PrepareForBuild" depends on it):
Task "GetReferenceAssemblyPaths"
Done executing task "GetReferenceAssemblyPaths".
Done building target "GetReferenceAssemblyPaths" in project "Selltis.Core.csproj".
Target "PrepareForBuild" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "CoreBuild" depends on it):
Task "FindAppConfigFile"
  Found "app.config".
Done executing task "FindAppConfigFile".
Task "MakeDir"
Done executing task "MakeDir".
Done building target "PrepareForBuild" in project "Selltis.Core.csproj".
Target "PreBuildEvent" skipped, due to false condition; ('$(PreBuildEvent)'!='') was evaluated as (''!='').
Target "BeforeResolveReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "ResolveReferences" depends on it):
Done building target "BeforeResolveReferences" in project "Selltis.Core.csproj".
Target "AssignProjectConfiguration" skipped, due to false condition; ('$(CurrentSolutionConfigurationContents)' != '' or '@(ProjectReference)'!='') was evaluated as ('' != '' or ''!='').
Target "AssignProjectConfiguration" skipped, due to false condition; ('$(CurrentSolutionConfigurationContents)' != '' or '@(ProjectReference)'!='') was evaluated as ('' != '' or ''!='').
Target "_SplitProjectReferencesByFileExistence" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "ResolveProjectReferences" depends on it):
Task "ResolveNonMSBuildProjectOutput" skipped, due to false condition; ('$(BuildingInsideVisualStudio)'=='true' and '@(ProjectReferenceWithConfiguration)'!='') was evaluated as (''=='true' and ''!='').
Done building target "_SplitProjectReferencesByFileExistence" in project "Selltis.Core.csproj".
Target "ResolveProjectReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "ResolveReferences" depends on it):
Task "MSBuild" skipped, due to false condition; ('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and ('$(BuildingInsideVisualStudio)' == 'true' or '$(BuildProjectReferences)' != 'true') and '$(VisualStudioVersion)' != '10.0' and '@(_MSBuildProjectReferenceExistent)' != '') was evaluated as ('' == 'true' and '' != '' and ('' == 'true' or 'true' != 'true') and '11.0' != '10.0' and '' != '').
Task "MSBuild" skipped, due to false condition; ('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and ('$(BuildingInsideVisualStudio)' == 'true' or '$(BuildProjectReferences)' != 'true') and '$(VisualStudioVersion)' == '10.0' and '@(_MSBuildProjectReferenceExistent)' != '') was evaluated as ('' == 'true' and '' != '' and ('' == 'true' or 'true' != 'true') and '11.0' == '10.0' and '' != '').
Task "MSBuild" skipped, due to false condition; ('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and '$(BuildingInsideVisualStudio)' != 'true' and '$(BuildProjectReferences)' == 'true' and '@(_MSBuildProjectReferenceExistent)' != '') was evaluated as ('' == 'true' and '' != '' and '' != 'true' and 'true' == 'true' and '' != '').
Task "MSBuild" skipped, due to false condition; ('%(_MSBuildProjectReferenceExistent.BuildReference)' == 'true' and '@(ProjectReferenceWithConfiguration)' != '' and '$(BuildingProject)' == 'true' and '@(_MSBuildProjectReferenceExistent)' != '') was evaluated as ('' == 'true' and '' != '' and 'true' == 'true' and '' != '').
Task "Warning" skipped, due to false condition; ('@(ProjectReferenceWithConfiguration)' != '' and '@(_MSBuildProjectReferenceNonexistent)' != '') was evaluated as ('' != '' and '' != '').
Done building target "ResolveProjectReferences" in project "Selltis.Core.csproj".
Target "ResolveNativeReferences" skipped, due to false condition; ('@(NativeReference)'!='') was evaluated as (''!='').
Target "GetFrameworkPaths" skipped. Previously built successfully.
Target "GetReferenceAssemblyPaths" skipped. Previously built successfully.
Target "PrepareForBuild" skipped. Previously built successfully.
Target "GetInstalledSDKLocations" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "ResolveSDKReferences" depends on it):
Task "GetInstalledSDKLocations" skipped, due to false condition; ('@(SDKReference)' != '') was evaluated as ('' != '').
Done building target "GetInstalledSDKLocations" in project "Selltis.Core.csproj".
Target "ResolveSDKReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "ResolveAssemblyReferences" depends on it):
Task "ResolveSDKReference" skipped, due to false condition; ('@(SDKReference)'!='') was evaluated as (''!='').
Done building target "ResolveSDKReferences" in project "Selltis.Core.csproj".
Target "ResolveSDKReferences" skipped. Previously built successfully.
Target "ExpandSDKReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "ResolveAssemblyReferences" depends on it):
Task "GetSDKReferenceFiles" skipped, due to false condition; ('@(ResolvedSDKReference)'!='') was evaluated as (''!='').
Done building target "ExpandSDKReferences" in project "Selltis.Core.csproj".
Target "ResolveAssemblyReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "ResolveReferences" depends on it):
Task "ResolveAssemblyReference"
  TargetFrameworkMoniker:
      .NETFramework,Version=v4.8
  TargetFrameworkMonikerDisplayName:
      .NET Framework 4.8
  TargetedRuntimeVersion:
      v4.0.30319
  Assemblies:
      Microsoft.VisualBasic
      Microsoft.WindowsAzure.Configuration
          HintPath = '..\packages\CommonDLLs\Microsoft.WindowsAzure.Configuration.dll'
      Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL
          HintPath = '..\packages\CommonDLLs\Microsoft.WindowsAzure.Storage.dll'
          SpecificVersion = 'False'
      Microsoft.WindowsAzure.StorageClient
          HintPath = '..\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll'
      Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL
          HintPath = '..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll'
      PublicDomain, Version=0.2.47.0, Culture=neutral, PublicKeyToken=fd3f43b5776a962b, processorArchitecture=MSIL
          HintPath = '..\packages\CommonDLLs\PublicDomain.dll'
          SpecificVersion = 'False'
      Select.HtmlToPdf
          HintPath = '..\Common\Select.HtmlToPdf.dll'
      Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL
          HintPath = '..\..\..\SourceCode\Selltis\Selltis6.0\Selltis.BusinessLogic\bin\Debug\Selltis.BusinessLogic.dll'
          SpecificVersion = 'False'
      System
      System.configuration
      System.Device
      System.Drawing
      System.Web
      System.Web.DynamicData
      System.Web.Entity
      System.Web.Extensions
      System.Web.Mvc
          HintPath = '..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll'
      System.Web.Services
      System.Windows.Forms.DataVisualization
      System.Xml.Linq
      System.Data.DataSetExtensions
      Microsoft.CSharp
      System.Data
      System.Xml
      System.Core
  AssemblyFiles:
      C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll
  CandidateAssemblyFiles:
  TargetFrameworkDirectories:
      C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\,C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\
  InstalledAssemblyTables:
  IgnoreInstalledAssemblyTable:
      False
  SearchPaths:
      {CandidateAssemblyFiles}
      {HintPathFromItem}
      {TargetFrameworkDirectory}
      {Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}
      {AssemblyFolders}
      {GAC}
      {RawFileName}
      bin\Debug\
  AllowedAssemblyExtensions:
      .winmd
      .dll
      .exe
  AllowedRelatedFileExtensions:
      .pdb
      .xml
      .pri
  AppConfigFile:
      
  AutoUnify:
      True
  CopyLocalDependenciesWhenParentReferenceInGac:
      True
  FindDependencies:
      True
  TargetProcessorArchitecture:
      msil
  StateFile:
      obj\Debug\Selltis.Core.csprojResolveAssemblyReference.cache
  InstalledAssemblySubsetTables:
  IgnoreInstalledAssemblySubsetTable:
      False
  TargetFrameworkSubsets:
  FullTargetFrameworkSubsetNames:
      Full
  ProfileName:
      
  FullFrameworkFolders:
      C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\
  LatestTargetFrameworkDirectories:
  ProfileTablesLocation:
  Unified primary reference "mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll".
      Reference found at search path location "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Microsoft.VisualBasic, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.VisualBasic.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.VisualBasic.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Microsoft.WindowsAzure.Configuration, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.Configuration.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Microsoft.WindowsAzure.Storage, Version=8.1.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.Storage.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Microsoft.WindowsAzure.StorageClient, Version=1.7.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v2.0.50727".
  Unified primary reference "Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed".
      Using this version instead of original version "6.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.Storage.dll" because AutoUnify is 'true'.
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll".
      Reference found at search path location "{HintPathFromItem}".
      Found related file "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.xml".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "PublicDomain, Version=0.2.50.0, Culture=neutral, PublicKeyToken=fd3f43b5776a962b".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll".
      Reference found at search path location "{HintPathFromItem}".
      The ImageRuntimeVersion for this reference is "v2.0.50727".
  Primary reference "Select.HtmlToPdf, Version=21.1.0.0, Culture=neutral, PublicKeyToken=e0ae9f6e27a97018".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\Select.HtmlToPdf.dll".
      Reference found at search path location "{HintPathFromItem}".
      Found embedded scatter file "Select.Html.dep".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Selltis.BusinessLogic.dll".
      Reference found at search path location "bin\Debug\".
          For SearchPath "{HintPathFromItem}".
          Considered "..\..\..\SourceCode\Selltis\Selltis6.0\Selltis.BusinessLogic\bin\Debug\Selltis.BusinessLogic.dll", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Selltis.BusinessLogic.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Selltis.BusinessLogic.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Selltis.BusinessLogic.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Selltis.BusinessLogic.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Selltis.BusinessLogic.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Selltis.BusinessLogic.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Selltis.BusinessLogic.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Selltis.BusinessLogic.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Selltis.BusinessLogic.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Selltis.BusinessLogic.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Selltis.BusinessLogic.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Selltis.BusinessLogic.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Selltis.BusinessLogic", which was not found in the GAC.
          For SearchPath "{RawFileName}".
          Considered treating "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL" as a file name, but it didn't exist.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Selltis.BusinessLogic.winmd", but it didn't exist.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Device, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Device.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Device.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Web.DynamicData, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.DynamicData.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.DynamicData.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Web.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Entity.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Entity.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Extensions.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Extensions.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Web.Mvc, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll".
      Reference found at search path location "{HintPathFromItem}".
      Found related file "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.xml".
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Web.Services, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Services.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Services.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Windows.Forms.DataVisualization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.DataVisualization.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.DataVisualization.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Xml.Linq, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "3.5.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "System.Data.DataSetExtensions, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Primary reference "Microsoft.CSharp, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Xml, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified primary reference "System.Core, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "3.5.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.winmd", but it didn't exist.
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "Microsoft.Data.Services.Client, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "Microsoft.Data.Services.Client, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.Services.Client.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.Services.Client.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.Services.Client.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.Services.Client.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.Services.Client.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.Services.Client.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.Services.Client.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.Services.Client.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.Services.Client.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.Services.Client.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.Services.Client.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.Services.Client.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.Services.Client.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.Services.Client.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.Services.Client.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Microsoft.Data.Services.Client, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Microsoft.Data.Services.Client.winmd", but it didn't exist.
          Considered "bin\Debug\Microsoft.Data.Services.Client.dll", but it didn't exist.
          Considered "bin\Debug\Microsoft.Data.Services.Client.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL".
  Dependency "Microsoft.Data.OData, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "Microsoft.Data.OData, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.OData.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.OData.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.OData.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.OData.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.OData.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.OData.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.OData.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.OData.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.OData.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.OData.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.OData.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.OData.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.OData.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.OData.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.OData.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Microsoft.Data.OData, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Microsoft.Data.OData.winmd", but it didn't exist.
          Considered "bin\Debug\Microsoft.Data.OData.dll", but it didn't exist.
          Considered "bin\Debug\Microsoft.Data.OData.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL".
  Dependency "Microsoft.Data.Edm, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "Microsoft.Data.Edm, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.Edm.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.Edm.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Data.Edm.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.Edm.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.Edm.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Data.Edm.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.Edm.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.Edm.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Data.Edm.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.Edm.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.Edm.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Data.Edm.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.Edm.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.Edm.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Data.Edm.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Microsoft.Data.Edm, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Microsoft.Data.Edm.winmd", but it didn't exist.
          Considered "bin\Debug\Microsoft.Data.Edm.dll", but it didn't exist.
          Considered "bin\Debug\Microsoft.Data.Edm.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL".
  Dependency "Microsoft.Azure.KeyVault.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "Microsoft.Azure.KeyVault.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Azure.KeyVault.Core.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Azure.KeyVault.Core.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.Azure.KeyVault.Core.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Azure.KeyVault.Core.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Azure.KeyVault.Core.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Azure.KeyVault.Core.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Azure.KeyVault.Core.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Azure.KeyVault.Core.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Azure.KeyVault.Core.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Azure.KeyVault.Core.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Azure.KeyVault.Core.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Azure.KeyVault.Core.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Azure.KeyVault.Core.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Azure.KeyVault.Core.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Azure.KeyVault.Core.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Microsoft.Azure.KeyVault.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Microsoft.Azure.KeyVault.Core.winmd", but it didn't exist.
          Considered "bin\Debug\Microsoft.Azure.KeyVault.Core.dll", but it didn't exist.
          Considered "bin\Debug\Microsoft.Azure.KeyVault.Core.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL".
  Unified Dependency "System.Data.Services.Client, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "3.5.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Services.Client.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Data.Services.Client.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Data.Services.Client.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Data.Services.Client.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Services.Client.winmd", but it didn't exist.
      Required by "Microsoft.WindowsAzure.StorageClient".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "System.Numerics, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Numerics.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Newtonsoft.Json.13.0.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Newtonsoft.Json.13.0.3\lib\net45\System.Numerics.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Newtonsoft.Json.13.0.3\lib\net45\System.Numerics.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Newtonsoft.Json.13.0.3\lib\net45\System.Numerics.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Numerics.winmd", but it didn't exist.
      Required by "Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL".
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "System.Runtime.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Runtime.Serialization.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Newtonsoft.Json.13.0.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Newtonsoft.Json.13.0.3\lib\net45\System.Runtime.Serialization.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Newtonsoft.Json.13.0.3\lib\net45\System.Runtime.Serialization.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Newtonsoft.Json.13.0.3\lib\net45\System.Runtime.Serialization.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Runtime.Serialization.winmd", but it didn't exist.
      Required by "Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL".
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "Microsoft.WindowsAzure.Storage, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified Dependency "System.Transactions, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Transactions.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Transactions.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Transactions.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Transactions.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Transactions.winmd", but it didn't exist.
      Required by "PublicDomain, Version=0.2.47.0, Culture=neutral, PublicKeyToken=fd3f43b5776a962b, processorArchitecture=MSIL".
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Unified Dependency "System.Management, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Using this version instead of original version "2.0.0.0" in "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" because there is a more recent version of this framework file.
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Management.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Management.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Management.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\System.Management.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Management.winmd", but it didn't exist.
      Required by "PublicDomain, Version=0.2.47.0, Culture=neutral, PublicKeyToken=fd3f43b5776a962b, processorArchitecture=MSIL".
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\System.Windows.Forms.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\System.Windows.Forms.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\System.Windows.Forms.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.winmd", but it didn't exist.
      Required by "Select.HtmlToPdf".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "Google.GData.Client, Version=2.2.0.0, Culture=neutral, PublicKeyToken=04a59ca9b0273830".
      Could not resolve this reference. Could not locate the assembly "Google.GData.Client, Version=2.2.0.0, Culture=neutral, PublicKeyToken=04a59ca9b0273830". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Google.GData.Client.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Google.GData.Client.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Google.GData.Client.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Google.GData.Client.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Google.GData.Client.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Google.GData.Client.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Google.GData.Client.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Google.GData.Client.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Google.GData.Client.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Google.GData.Client.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Google.GData.Client.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Google.GData.Client.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Google.GData.Client.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Google.GData.Client.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Google.GData.Client.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Google.GData.Client, Version=2.2.0.0, Culture=neutral, PublicKeyToken=04a59ca9b0273830", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Google.GData.Client.winmd", but it didn't exist.
          Considered "bin\Debug\Google.GData.Client.dll", but it didn't exist.
          Considered "bin\Debug\Google.GData.Client.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
  Dependency "ephtmltopdf, Version=*******, Culture=neutral, PublicKeyToken=5b5f377bc08a4d32".
      Could not resolve this reference. Could not locate the assembly "ephtmltopdf, Version=*******, Culture=neutral, PublicKeyToken=5b5f377bc08a4d32". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\ephtmltopdf.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\ephtmltopdf.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\ephtmltopdf.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\ephtmltopdf.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\ephtmltopdf.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\ephtmltopdf.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\ephtmltopdf.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\ephtmltopdf.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\ephtmltopdf.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\ephtmltopdf.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\ephtmltopdf.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\ephtmltopdf.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\ephtmltopdf.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\ephtmltopdf.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\ephtmltopdf.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "ephtmltopdf, Version=*******, Culture=neutral, PublicKeyToken=5b5f377bc08a4d32", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\ephtmltopdf.winmd", but it didn't exist.
          Considered "bin\Debug\ephtmltopdf.dll", but it didn't exist.
          Considered "bin\Debug\ephtmltopdf.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
  Dependency "System.Web.ApplicationServices, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.ApplicationServices.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\System.Web.ApplicationServices.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\System.Web.ApplicationServices.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\System.Web.ApplicationServices.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.ApplicationServices.winmd", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "MailBee.NET, Version=11.2.0.590, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1".
      Could not resolve this reference. Could not locate the assembly "MailBee.NET, Version=11.2.0.590, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\MailBee.NET.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\MailBee.NET.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\MailBee.NET.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\MailBee.NET.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\MailBee.NET.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\MailBee.NET.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\MailBee.NET.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\MailBee.NET.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\MailBee.NET.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\MailBee.NET.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\MailBee.NET.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\MailBee.NET.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\MailBee.NET.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\MailBee.NET.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\MailBee.NET.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "MailBee.NET, Version=11.2.0.590, Culture=neutral, PublicKeyToken=cd85b70fb26f9fc1", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\MailBee.NET.winmd", but it didn't exist.
          Considered "bin\Debug\MailBee.NET.dll", but it didn't exist.
          Considered "bin\Debug\MailBee.NET.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
  Dependency "DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\DocumentFormat.OpenXml.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\DocumentFormat.OpenXml.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\DocumentFormat.OpenXml.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\DocumentFormat.OpenXml.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\DocumentFormat.OpenXml.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\DocumentFormat.OpenXml.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\DocumentFormat.OpenXml.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\DocumentFormat.OpenXml.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\DocumentFormat.OpenXml.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\DocumentFormat.OpenXml.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\DocumentFormat.OpenXml.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\DocumentFormat.OpenXml.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\DocumentFormat.OpenXml.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\DocumentFormat.OpenXml.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\DocumentFormat.OpenXml.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\DocumentFormat.OpenXml.winmd", but it didn't exist.
          Considered "bin\Debug\DocumentFormat.OpenXml.dll", but it didn't exist.
          Considered "bin\Debug\DocumentFormat.OpenXml.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
  Dependency "SautinSoft.Document, Version=4.5.6.17, Culture=neutral, PublicKeyToken=e759c76c7515592a".
      Could not resolve this reference. Could not locate the assembly "SautinSoft.Document, Version=4.5.6.17, Culture=neutral, PublicKeyToken=e759c76c7515592a". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\SautinSoft.Document.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\SautinSoft.Document.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\SautinSoft.Document.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\SautinSoft.Document.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\SautinSoft.Document.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\SautinSoft.Document.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\SautinSoft.Document.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\SautinSoft.Document.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\SautinSoft.Document.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\SautinSoft.Document.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\SautinSoft.Document.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\SautinSoft.Document.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\SautinSoft.Document.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\SautinSoft.Document.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\SautinSoft.Document.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "SautinSoft.Document, Version=4.5.6.17, Culture=neutral, PublicKeyToken=e759c76c7515592a", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\SautinSoft.Document.winmd", but it didn't exist.
          Considered "bin\Debug\SautinSoft.Document.dll", but it didn't exist.
          Considered "bin\Debug\SautinSoft.Document.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
  Dependency "Telerik.Windows.Documents.Core, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7".
      Could not resolve this reference. Could not locate the assembly "Telerik.Windows.Documents.Core, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Telerik.Windows.Documents.Core.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Telerik.Windows.Documents.Core.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Telerik.Windows.Documents.Core.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Telerik.Windows.Documents.Core.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Telerik.Windows.Documents.Core.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Telerik.Windows.Documents.Core.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Telerik.Windows.Documents.Core.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Telerik.Windows.Documents.Core.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Telerik.Windows.Documents.Core.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Telerik.Windows.Documents.Core.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Telerik.Windows.Documents.Core.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Telerik.Windows.Documents.Core.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Telerik.Windows.Documents.Core.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Telerik.Windows.Documents.Core.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Telerik.Windows.Documents.Core.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Telerik.Windows.Documents.Core, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Telerik.Windows.Documents.Core.winmd", but it didn't exist.
          Considered "bin\Debug\Telerik.Windows.Documents.Core.dll", but it didn't exist.
          Considered "bin\Debug\Telerik.Windows.Documents.Core.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
  Dependency "Telerik.Windows.Documents.Flow, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7".
      Could not resolve this reference. Could not locate the assembly "Telerik.Windows.Documents.Flow, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Telerik.Windows.Documents.Flow.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Telerik.Windows.Documents.Flow.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Telerik.Windows.Documents.Flow.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Telerik.Windows.Documents.Flow.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Telerik.Windows.Documents.Flow.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Telerik.Windows.Documents.Flow.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Telerik.Windows.Documents.Flow.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Telerik.Windows.Documents.Flow.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Telerik.Windows.Documents.Flow.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Telerik.Windows.Documents.Flow.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Telerik.Windows.Documents.Flow.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Telerik.Windows.Documents.Flow.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Telerik.Windows.Documents.Flow.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Telerik.Windows.Documents.Flow.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Telerik.Windows.Documents.Flow.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Telerik.Windows.Documents.Flow, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Telerik.Windows.Documents.Flow.winmd", but it didn't exist.
          Considered "bin\Debug\Telerik.Windows.Documents.Flow.dll", but it didn't exist.
          Considered "bin\Debug\Telerik.Windows.Documents.Flow.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
  Dependency "Telerik.Windows.Documents.Flow.FormatProviders.Pdf, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7".
      Could not resolve this reference. Could not locate the assembly "Telerik.Windows.Documents.Flow.FormatProviders.Pdf, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Telerik.Windows.Documents.Flow.FormatProviders.Pdf, Version=2019.1.215.40, Culture=neutral, PublicKeyToken=5803cfa389c90ce7", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.winmd", but it didn't exist.
          Considered "bin\Debug\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll", but it didn't exist.
          Considered "bin\Debug\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
  Dependency "WindowsBase, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\WindowsBase.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\WindowsBase.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\WindowsBase.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\WindowsBase.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\WindowsBase.winmd", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "System.ComponentModel.DataAnnotations, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.ComponentModel.DataAnnotations.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.ComponentModel.DataAnnotations.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.ComponentModel.DataAnnotations.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.ComponentModel.DataAnnotations.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.ComponentModel.DataAnnotations.winmd", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "System.Web.Mvc".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Razor.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Razor.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Razor.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Razor.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Razor.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.Razor.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.Razor.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.Razor.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.Razor.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.Razor.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.Razor.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\System.Web.Razor.winmd", but it didn't exist.
          Considered "bin\Debug\System.Web.Razor.dll", but it didn't exist.
          Considered "bin\Debug\System.Web.Razor.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "System.Web.Mvc".
  Dependency "System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.WebPages.Razor.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.WebPages.Razor.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.WebPages.Razor.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.WebPages.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.WebPages.Razor.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.WebPages.Razor.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.WebPages.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.WebPages.Razor.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.WebPages.Razor.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.WebPages.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.WebPages.Razor.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.WebPages.Razor.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.WebPages.Razor.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.WebPages.Razor.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.WebPages.Razor.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\System.Web.WebPages.Razor.winmd", but it didn't exist.
          Considered "bin\Debug\System.Web.WebPages.Razor.dll", but it didn't exist.
          Considered "bin\Debug\System.Web.WebPages.Razor.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "System.Web.Mvc".
  Dependency "System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.WebPages.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.WebPages.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.WebPages.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.WebPages.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.WebPages.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.WebPages.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.WebPages.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.WebPages.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\System.Web.WebPages.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.WebPages.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.WebPages.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\System.Web.WebPages.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.WebPages.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.WebPages.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\System.Web.WebPages.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\System.Web.WebPages.winmd", but it didn't exist.
          Considered "bin\Debug\System.Web.WebPages.dll", but it didn't exist.
          Considered "bin\Debug\System.Web.WebPages.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "System.Web.Mvc".
  Dependency "System.Runtime.Caching, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Runtime.Caching.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Runtime.Caching.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Runtime.Caching.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Runtime.Caching.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Runtime.Caching.winmd", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "System.Web.Mvc".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35".
      Could not resolve this reference. Could not locate the assembly "Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\Microsoft.Web.Infrastructure.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\Microsoft.Web.Infrastructure.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\Microsoft.Web.Infrastructure.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Web.Infrastructure.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Web.Infrastructure.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.Web.Infrastructure.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Web.Infrastructure.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Web.Infrastructure.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\Microsoft.Web.Infrastructure.exe", but it didn't exist.
          For SearchPath "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}".
          Considered AssemblyFoldersEx locations.
          For SearchPath "{AssemblyFolders}".
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Web.Infrastructure.winmd", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Web.Infrastructure.dll", but it didn't exist.
          Considered "C:\Program Files\IIS\Microsoft Web Deploy V3\Microsoft.Web.Infrastructure.exe", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Web.Infrastructure.winmd", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Web.Infrastructure.dll", but it didn't exist.
          Considered "C:\Program Files (x86)\Microsoft.NET\ADOMD.NET\150\Microsoft.Web.Infrastructure.exe", but it didn't exist.
          For SearchPath "{GAC}".
          Considered "Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35", which was not found in the GAC.
          For SearchPath "bin\Debug\".
          Considered "bin\Debug\Microsoft.Web.Infrastructure.winmd", but it didn't exist.
          Considered "bin\Debug\Microsoft.Web.Infrastructure.dll", but it didn't exist.
          Considered "bin\Debug\Microsoft.Web.Infrastructure.exe", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "System.Web.Mvc".
  Dependency "System.Data.Linq, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Linq.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Data.Linq.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Data.Linq.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Data.Linq.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Linq.winmd", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "System.Web.Mvc".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  Dependency "System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089".
      Resolved file path is "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Entity.dll".
      Reference found at search path location "{TargetFrameworkDirectory}".
          For SearchPath "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45".
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Data.Entity.winmd", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Data.Entity.dll", but it didn't exist.
          Considered "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Data.Entity.exe", but it didn't exist.
          For SearchPath "{TargetFrameworkDirectory}".
          Considered "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Entity.winmd", but it didn't exist.
      Required by "Selltis.BusinessLogic, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL".
      Required by "System.Web.Mvc".
      This reference is not "CopyLocal" because it's a prerequisite file.
      The ImageRuntimeVersion for this reference is "v4.0.30319".
  AssemblyFoldersEx location: "{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}"
          C:\Program Files (x86)\Microsoft.NET\Primary Interop Assemblies\
          C:\Program Files (x86)\Common Files\Microsoft Shared\MSEnv\PublicAssemblies
          C:\Program Files (x86)\Microsoft Chart Controls\Assemblies
          C:\Program Files\Microsoft SQL Server\150\DTS\Tasks
          C:\Program Files\Microsoft SQL Server\150\DTS\PipelineComponents\
          C:\Program Files\Microsoft SQL Server\150\DTS\ForEachEnumerators
          C:\Program Files\Microsoft SQL Server\150\DTS\Connections\
Done executing task "ResolveAssemblyReference".
Done building target "ResolveAssemblyReferences" in project "Selltis.Core.csproj".
Target "ResolveComReferences" skipped, due to false condition; ('@(COMReference)'!='' or '@(COMFileReference)'!='') was evaluated as (''!='' or ''!='').
Target "AfterResolveReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "ResolveReferences" depends on it):
Done building target "AfterResolveReferences" in project "Selltis.Core.csproj".
Target "GetReferenceAssemblyPaths" skipped. Previously built successfully.
Target "ImplicitlyExpandDesignTimeFacades" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.NETFramework.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "ResolveReferences" depends on it):
Task "Message" skipped, due to false condition; ('%(ReferencePath.ResolvedFrom)' == 'ImplicitlyExpandDesignTimeFacades') was evaluated as ('{TargetFrameworkDirectory}' == 'ImplicitlyExpandDesignTimeFacades').
Task "Message" skipped, due to false condition; ('%(ReferencePath.ResolvedFrom)' == 'ImplicitlyExpandDesignTimeFacades') was evaluated as ('{HintPathFromItem}' == 'ImplicitlyExpandDesignTimeFacades').
Task "Message" skipped, due to false condition; ('%(ReferencePath.ResolvedFrom)' == 'ImplicitlyExpandDesignTimeFacades') was evaluated as ('C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll' == 'ImplicitlyExpandDesignTimeFacades').
Task "Message" skipped, due to false condition; ('%(ReferencePath.ResolvedFrom)' == 'ImplicitlyExpandDesignTimeFacades') was evaluated as ('bin\Debug\' == 'ImplicitlyExpandDesignTimeFacades').
Done building target "ImplicitlyExpandDesignTimeFacades" in project "Selltis.Core.csproj".
Target "ResolveReferences" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "CoreBuild" depends on it):
Done building target "ResolveReferences" in project "Selltis.Core.csproj".
Target "ValidationExtension" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.WorkflowBuildExtensions.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PrepareResources" depends on it):
Task "WorkflowBuildMessageTask" skipped, due to false condition; ('$(SkipWorkflowValidation)'!='' and '$(SkipWorkflowValidation)'!='true' and '$(SkipWorkflowValidation)'!='false') was evaluated as (''!='' and ''!='true' and ''!='false').
Done building target "ValidationExtension" in project "Selltis.Core.csproj".
Target "ExpressionBuildExtension" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.WorkflowBuildExtensions.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PrepareResources" depends on it):
Task "WorkflowBuildMessageTask" skipped, due to false condition; ('$(DisableWorkflowCompiledExpressions)'!='' and '$(DisableWorkflowCompiledExpressions)'!='true' and '$(DisableWorkflowCompiledExpressions)'!='false') was evaluated as (''!='' and ''!='true' and ''!='false').
Done building target "ExpressionBuildExtension" in project "Selltis.Core.csproj".
Target "XamlMarkupCompilePass1" skipped, due to false condition; ('@(XamlPage)' != '' or '@(XamlAppDef)' != '') was evaluated as ('' != '' or '' != '').
Target "XamlMarkupCompileReadGeneratedFileList" skipped, due to false condition; ('@(XamlPage)' != '' or '@(XamlAppDef)' != '') was evaluated as ('' != '' or '' != '').
Target "XamlMarkupCompileAddFilesGenerated" skipped, due to false condition; ('@(XamlPage)' != '' or '@(XamlAppDef)' != '') was evaluated as ('' != '' or '' != '').
Target "XamlMarkupCompilePass2" skipped, due to false condition; ('$(XamlRequiresCompilationPass2)' == 'true' ) was evaluated as ('false' == 'true' ).
Target "XamlMarkupCompileReadPass2Flag" skipped, due to false condition; ('@(XamlPage)' != '' or '@(XamlAppDef)' != '') was evaluated as ('' != '' or '' != '').
Target "XamlMarkupCompileAddExtensionFilesGenerated" skipped, due to false condition; ('@(XamlPage)' != '' or '@(XamlAppDef)' != '') was evaluated as ('' != '' or '' != '').
Target "AddDeferredValidationErrorsFileToFileWrites" skipped, due to false condition; (Exists('$(DeferredValidationErrorsFileName)')) was evaluated as (Exists('obj\Debug\\AC2C1ABA-CCF6-44D4-8127-588FD4D0A860-DeferredValidationErrors.xml')).
Target "ReportValidationBuildExtensionErrors" skipped, due to false condition; ('$(SkipWorkflowValidation)' != 'true' and ('@(XamlPage)' != '' or '@(XamlAppDef)' != '')) was evaluated as ('' != 'true' and ('' != '' or '' != '')).
Target "MarkupCompilePass1" skipped, due to false condition; ('@(Page)' != '' or '@(ApplicationDefinition)' != '' ) was evaluated as ('' != '' or '' != '' ).
Target "AfterMarkupCompilePass1" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.WinFX.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PrepareResources" depends on it):
Done building target "AfterMarkupCompilePass1" in project "Selltis.Core.csproj".
Target "MarkupCompilePass2ForMainAssembly" skipped, due to false condition; ('$(_RequireMCPass2ForMainAssembly)' == 'true' ) was evaluated as ('false' == 'true' ).
Target "FileClassification" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.WinFX.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PrepareResources" depends on it):
Task "FileClassifier" skipped, due to false condition; ('@(GeneratedBaml)' != '' or '@(Resource)' != '' or '@(Font)' != '') was evaluated as ('' != '' or '' != '' or '' != '').
Task "Message" skipped, due to false condition; ('$(MSBuildTargetsVerbose)'=='true') was evaluated as (''=='true').
Task "Message" skipped, due to false condition; ('$(MSBuildTargetsVerbose)'=='true') was evaluated as (''=='true').
Done building target "FileClassification" in project "Selltis.Core.csproj".
Target "MainResourcesGeneration" skipped, due to false condition; ('@(MainEmbeddedFiles)' != '') was evaluated as ('' != '').
Target "AssignWinFXEmbeddedResource" skipped, due to false condition; ('@(WinFXEmbeddedResource)' != '') was evaluated as ('' != '').
Target "AssignTargetPaths" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PrepareResourceNames" depends on it):
Task "AssignTargetPath"
Done executing task "AssignTargetPath".
Task "AssignTargetPath"
Done executing task "AssignTargetPath".
Task "AssignTargetPath"
Done executing task "AssignTargetPath".
Task "AssignTargetPath"
Done executing task "AssignTargetPath".
Task "AssignTargetPath" skipped, due to false condition; ('@(_DeploymentBaseManifestWithTargetPath)'=='' and '%(None.Extension)'=='.manifest') was evaluated as (''=='' and '.config'=='.manifest').
Done building target "AssignTargetPaths" in project "Selltis.Core.csproj".
Target "AssignTargetPaths" skipped. Previously built successfully.
Target "SplitResourcesByCulture" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PrepareResourceNames" depends on it):
Task "Warning" skipped, due to false condition; ('@(ResxWithNoCulture)'!='') was evaluated as (''!='').
Task "Warning" skipped, due to false condition; ('@(ResxWithCulture)'!='') was evaluated as (''!='').
Task "Warning" skipped, due to false condition; ('@(NonResxWithCulture)'!='') was evaluated as (''!='').
Task "Warning" skipped, due to false condition; ('@(NonResxWithNoCulture)'!='') was evaluated as (''!='').
Task "AssignCulture"
  Culture of "" was assigned to file "Properties\Resources.resx".
Done executing task "AssignCulture".
Done building target "SplitResourcesByCulture" in project "Selltis.Core.csproj".
Target "CreateManifestResourceNames" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.CSharp.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PrepareResourceNames" depends on it):
Task "CreateCSharpManifestResourceName"
  Root namespace is 'Selltis.Core'.
  Resource file 'Properties\Resources.resx' doesn't depend on any other file.
  Resource file 'Properties\Resources.resx' gets manifest resource name 'Selltis.Core.Properties.Resources'.
Done executing task "CreateCSharpManifestResourceName".
Task "CreateCSharpManifestResourceName" skipped, due to false condition; ('%(EmbeddedResource.ManifestResourceName)' == '' and '%(EmbeddedResource.WithCulture)' == 'true' and '%(EmbeddedResource.Type)' == 'Non-Resx') was evaluated as ('' == '' and 'false' == 'true' and 'Resx' == 'Non-Resx').
Done building target "CreateManifestResourceNames" in project "Selltis.Core.csproj".
Target "CreateCustomManifestResourceNames" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PrepareResourceNames" depends on it):
Done building target "CreateCustomManifestResourceNames" in project "Selltis.Core.csproj".
Target "PrepareResourceNames" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PrepareResources" depends on it):
Done building target "PrepareResourceNames" in project "Selltis.Core.csproj".
Target "ResolveAssemblyReferences" skipped. Previously built successfully.
Target "SplitResourcesByCulture" skipped. Previously built successfully.
Target "BeforeResGen" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "ResGen" depends on it):
Done building target "BeforeResGen" in project "Selltis.Core.csproj".
Target "CoreResGen" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "ResGen" depends on it):
Task "GenerateResource"
  No resources are out of date with respect to their source files. Skipping resource generation.
Done executing task "GenerateResource".
Task "GenerateResource" skipped, due to false condition; ('%(EmbeddedResource.Type)' == 'Resx' and '%(EmbeddedResource.GenerateResource)' != 'false' and '$(GenerateResourceMSBuildRuntime)' == 'CLR2') was evaluated as ('Resx' == 'Resx' and '' != 'false' and 'CurrentRuntime' == 'CLR2').
Done building target "CoreResGen" in project "Selltis.Core.csproj".
Target "AfterResGen" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "ResGen" depends on it):
Done building target "AfterResGen" in project "Selltis.Core.csproj".
Target "ResGen" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PrepareResources" depends on it):
Done building target "ResGen" in project "Selltis.Core.csproj".
Target "CompileLicxFiles" skipped, due to false condition; ('@(_LicxFile)'!='') was evaluated as (''!='').
Target "PrepareResources" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "CoreBuild" depends on it):
Done building target "PrepareResources" in project "Selltis.Core.csproj".
Target "ResolveKeySource" skipped, due to false condition; ($(SignManifests) == 'true' or $(SignAssembly) == 'true') was evaluated as ( == 'true' or  == 'true').
Target "ResolveReferences" skipped. Previously built successfully.
Target "ResolveKeySource" skipped, due to false condition; ($(SignManifests) == 'true' or $(SignAssembly) == 'true') was evaluated as ( == 'true' or  == 'true').
Target "ResolveComReferences" skipped, due to false condition; ('@(COMReference)'!='' or '@(COMFileReference)'!='') was evaluated as (''!='' or ''!='').
Target "ResolveNativeReferences" skipped, due to false condition; ('@(NativeReference)'!='') was evaluated as (''!='').
Target "_SetExternalWin32ManifestProperties" skipped, due to false condition; ('$(GenerateClickOnceManifests)'=='true' or '@(NativeReference)'!='' or '@(ResolvedIsolatedComModules)'!='') was evaluated as (''=='true' or ''!='' or ''!='').
Target "_SetEmbeddedWin32ManifestProperties" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "SetWin32ManifestProperties" depends on it):
Task "GetFrameworkPath"
Done executing task "GetFrameworkPath".
Done building target "_SetEmbeddedWin32ManifestProperties" in project "Selltis.Core.csproj".
Target "SetWin32ManifestProperties" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "Compile" depends on it):
Done building target "SetWin32ManifestProperties" in project "Selltis.Core.csproj".
Target "_GenerateCompileInputs" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "Compile" depends on it):
Task "Warning" skipped, due to false condition; ('@(ManifestResourceWithNoCulture)'!='' and '%(ManifestResourceWithNoCulture.EmittedForCompatibilityOnly)'=='') was evaluated as ('obj\Debug\Selltis.Core.Properties.Resources.resources'!='' and 'true'=='').
Task "Warning" skipped, due to false condition; ('@(ManifestNonResxWithNoCultureOnDisk)'!='' and '%(ManifestNonResxWithNoCultureOnDisk.EmittedForCompatibilityOnly)'=='') was evaluated as (''!='' and ''=='').
Done building target "_GenerateCompileInputs" in project "Selltis.Core.csproj".
Target "PrepareForBuild" skipped. Previously built successfully.
Target "GetReferenceAssemblyPaths" skipped. Previously built successfully.
Target "_SetTargetFrameworkMonikerAttribute" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.CSharp.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "GenerateTargetFrameworkMonikerAttribute" depends on it):
Done building target "_SetTargetFrameworkMonikerAttribute" in project "Selltis.Core.csproj".
Target "GenerateTargetFrameworkMonikerAttribute" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "BeforeCompile" depends on it):
Skipping target "GenerateTargetFrameworkMonikerAttribute" because all output files are up-to-date with respect to the input files.
Input files: C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets
Output files: C:\Users\<USER>\AppData\Local\Temp\.NETFramework,Version=v4.8.AssemblyAttributes.cs
Done building target "GenerateTargetFrameworkMonikerAttribute" in project "Selltis.Core.csproj".
Target "GenerateAdditionalSources" skipped, due to false condition; ('@(AssemblyAttributes)' != '' and '$(GenerateAdditionalSources)' == 'true') was evaluated as ('' != '' and '' == 'true').
Target "BeforeCompile" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "Compile" depends on it):
Done building target "BeforeCompile" in project "Selltis.Core.csproj".
Target "_TimeStampBeforeCompile" skipped, due to false condition; ('$(RunPostBuildEvent)'=='OnOutputUpdated' or ('$(RegisterForComInterop)'=='true' and '$(OutputType)'=='library')) was evaluated as (''=='OnOutputUpdated' or (''=='true' and 'Library'=='library')).
Target "_ComputeNonExistentFileProperty" skipped, due to false condition; (('$(BuildingInsideVisualStudio)' == 'true') and ('$(BuildingOutOfProcess)' != 'true') and (('$(BuildingProject)' == 'false') or ('$(UseHostCompilerIfAvailable)' == 'true'))) was evaluated as (('' == 'true') and ('' != 'true') and (('true' == 'false') or ('true' == 'true'))).
Target "PreXsdCodeGen" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.ServiceModel.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "CoreCompile" depends on it):
Task "CallTarget"
Target "CleanXsdCodeGen" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.ServiceModel.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "PreXsdCodeGen" depends on it):
Task "Delete"
Done executing task "Delete".
Done building target "CleanXsdCodeGen" in project "Selltis.Core.csproj".
Done executing task "CallTarget".
Done building target "PreXsdCodeGen" in project "Selltis.Core.csproj".
Target "XsdCodeGen" skipped, due to false condition; ( '$(XsdCodeGenPreCondition)' == 'True' ) was evaluated as ( 'False' == 'True' ).
Target "CoreCompile" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.CSharp.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "Compile" depends on it):
Building target "CoreCompile" completely.
Input file "C:\Users\<USER>\AppData\Local\Temp\.NETFramework,Version=v4.8.AssemblyAttributes.cs" is newer than output file "obj\Debug\Selltis.Core.dll".
Task "Csc"
  C:\Windows\Microsoft.NET\Framework\v4.0.30319\Csc.exe /noconfig /nowarn:1701,1702 /nostdlib+ /errorreport:prompt /warn:4 /define:DEBUG;TRACE /highentropyva+ /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.VisualBasic.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.Configuration.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.Storage.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\CommonDLLs\PublicDomain.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Common\Select.HtmlToPdf.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\bin\Debug\Selltis.BusinessLogic.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Device.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.DynamicData.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Entity.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Extensions.dll" /reference:"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Services.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.DataVisualization.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dll" /reference:"C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.dll" /debug+ /debug:full /filealign:512 /optimize- /out:obj\Debug\Selltis.Core.dll /subsystemversion:6.00 /resource:obj\Debug\Selltis.Core.Properties.Resources.resources /target:library /utf8output Calendar.cs Chart.cs Map.cs Properties\Resources.Designer.cs RenderView.cs ScriptsCLM.cs DBView.cs Desktop.cs File_Icons.cs Form.cs FormEvents.cs Grid.cs GridColumn.cs History.cs LinkBox.cs MessageBox.cs Properties\AssemblyInfo.cs Report.cs ScriptManager.cs Scripts.cs SessionViewInfo.cs Util.cs View.cs "C:\Users\<USER>\AppData\Local\Temp\.NETFramework,Version=v4.8.AssemblyAttributes.cs"
  Microsoft (R) Visual C# Compiler version 4.8.9232.0
  
  for C# 5
  Copyright (C) Microsoft Corporation. All rights reserved.
  
  
  
  This compiler is provided as part of the Microsoft (R) .NET Framework, but only supports language versions up to C# 5, which is no longer the latest version. For compilers that support newer versions of the C# programming language, see http://go.microsoft.com/fwlink/?LinkID=533240
  
Util.cs(28,7): error CS1041: Identifier expected; 'static' is a keyword [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj]
Util.cs(28,14): error CS1518: Expected class, delegate, enum, interface, or struct [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj]
  The command exited with code 1.
Done executing task "Csc" -- FAILED.
Done building target "CoreCompile" in project "Selltis.Core.csproj" -- FAILED.
Target "_CheckForCompileOutputs" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "_CleanGetCurrentAndPriorFileWrites" depends on it):
Done building target "_CheckForCompileOutputs" in project "Selltis.Core.csproj".
Target "_SGenCheckForOutputs" skipped, due to false condition; ('$(_SGenGenerateSerializationAssembliesConfig)' == 'On' or ('@(WebReferenceUrl)'!='' and '$(_SGenGenerateSerializationAssembliesConfig)' == 'Auto')) was evaluated as ('Off' == 'On' or (''!='' and 'Off' == 'Auto')).
Target "_CleanGetCurrentAndPriorFileWrites" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "_CleanRecordFileWrites" depends on it):
Task "ReadLinesFromFile"
Done executing task "ReadLinesFromFile".
Task "ConvertToAbsolutePath"
Done executing task "ConvertToAbsolutePath".
Task "FindUnderPath"
  Comparison path is "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core".
Done executing task "FindUnderPath".
Task "FindUnderPath"
  Comparison path is "bin\Debug\".
Done executing task "FindUnderPath".
Task "FindUnderPath"
  Comparison path is "obj\Debug\".
Done executing task "FindUnderPath".
Task "RemoveDuplicates"
Done executing task "RemoveDuplicates".
Done building target "_CleanGetCurrentAndPriorFileWrites" in project "Selltis.Core.csproj".
Target "_CleanRecordFileWrites" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (target "CoreBuild" depends on it):
Task "RemoveDuplicates"
Done executing task "RemoveDuplicates".
Task "MakeDir"
Done executing task "MakeDir".
Task "WriteLinesToFile"
Done executing task "WriteLinesToFile".
Done building target "_CleanRecordFileWrites" in project "Selltis.Core.csproj".
Done Building Project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (default targets) -- FAILED.
Project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (1) is building "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis6.0\Selltis.MVC.csproj" (4) on node 1 (default targets).
Building with tools version "12.0".
C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis6.0\Selltis.MVC.csproj(4809,3): error MSB4019: The imported project "C:\Program Files (x86)\MSBuild\Microsoft\VisualStudio\v11.0\WebApplications\Microsoft.WebApplication.targets" was not found. Confirm that the path in the <Import> declaration is correct, and that the file exists on disk.
Done Building Project "C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis6.0\Selltis.MVC.csproj" (default targets) -- FAILED.
Done executing task "MSBuild" -- FAILED.
Done building target "ResolveProjectReferences" in project "APICodex.csproj" -- FAILED.
Target "_CheckForCompileOutputs" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (target "_CleanGetCurrentAndPriorFileWrites" depends on it):
Done building target "_CheckForCompileOutputs" in project "APICodex.csproj".
Target "_SGenCheckForOutputs" skipped, due to false condition; ('$(_SGenGenerateSerializationAssembliesConfig)' == 'On' or ('@(WebReferenceUrl)'!='' and '$(_SGenGenerateSerializationAssembliesConfig)' == 'Auto')) was evaluated as ('Off' == 'On' or (''!='' and 'Off' == 'Auto')).
Target "_CleanGetCurrentAndPriorFileWrites" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (target "_CleanRecordFileWrites" depends on it):
Task "ReadLinesFromFile"
Done executing task "ReadLinesFromFile".
Task "ConvertToAbsolutePath"
Done executing task "ConvertToAbsolutePath".
Task "FindUnderPath"
  Comparison path is "C:\Selltis\SourceCodeMerged Dev-c#\APICodex".
Done executing task "FindUnderPath".
Task "FindUnderPath"
  Comparison path is "bin\".
Done executing task "FindUnderPath".
Task "FindUnderPath"
  Comparison path is "obj\Debug\".
Done executing task "FindUnderPath".
Task "RemoveDuplicates"
Done executing task "RemoveDuplicates".
Done building target "_CleanGetCurrentAndPriorFileWrites" in project "APICodex.csproj".
Target "_CleanRecordFileWrites" in file "C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets" from project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (target "CoreBuild" depends on it):
Task "RemoveDuplicates"
Done executing task "RemoveDuplicates".
Task "MakeDir"
Done executing task "MakeDir".
Task "WriteLinesToFile"
Done executing task "WriteLinesToFile".
Done building target "_CleanRecordFileWrites" in project "APICodex.csproj".
Done Building Project "C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (Build target(s)) -- FAILED.

Build FAILED.

"C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (Build target) (1) ->
"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (default target) (2) ->
(ResolveAssemblyReferences target) -> 
  C:\Windows\Microsoft.NET\Framework\v4.0.30319\Microsoft.Common.targets(1605,5): warning MSB3247: Found conflicts between different versions of the same dependent assembly. [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]


"C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (Build target) (1) ->
"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj" (default target) (2) ->
(CoreCompile target) -> 
  clSQL.cs(3242,35): error CS1056: Unexpected character '$' [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
  clSQL.cs(18,7): error CS1041: Identifier expected; 'static' is a keyword [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
  clSQL.cs(18,14): error CS1518: Expected class, delegate, enum, interface, or struct [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
  clTransform.cs(36,7): error CS1041: Identifier expected; 'static' is a keyword [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
  clTransform.cs(36,14): error CS1518: Expected class, delegate, enum, interface, or struct [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
  ClUI.cs(3,7): error CS1041: Identifier expected; 'static' is a keyword [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]
  ClUI.cs(3,14): error CS1518: Expected class, delegate, enum, interface, or struct [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.BusinessLogic\Selltis.Data.csproj]


"C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (Build target) (1) ->
"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj" (default target) (3) ->
  Util.cs(28,7): error CS1041: Identifier expected; 'static' is a keyword [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj]
  Util.cs(28,14): error CS1518: Expected class, delegate, enum, interface, or struct [C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis.Core\Selltis.Core.csproj]


"C:\Selltis\SourceCodeMerged Dev-c#\APICodex\APICodex.csproj" (Build target) (1) ->
"C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis6.0\Selltis.MVC.csproj" (default target) (4) ->
  C:\Selltis\SourceCodeMerged Dev-c#\Selltis6.0\Selltis6.0\Selltis.MVC.csproj(4809,3): error MSB4019: The imported project "C:\Program Files (x86)\MSBuild\Microsoft\VisualStudio\v11.0\WebApplications\Microsoft.WebApplication.targets" was not found. Confirm that the path in the <Import> declaration is correct, and that the file exists on disk.

    1 Warning(s)
    10 Error(s)

Time Elapsed 00:00:01.79
